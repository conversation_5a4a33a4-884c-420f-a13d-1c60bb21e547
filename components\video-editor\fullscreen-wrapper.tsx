"use client";

import React, { useEffect } from "react";
import { useSidebar } from "@/lib/contexts/sidebar-context";
import { cn } from "@/lib/utils";

interface FullscreenWrapperProps {
  children: React.ReactNode;
  className?: string;
}

export function FullscreenWrapper({ children, className }: FullscreenWrapperProps) {
  const { isFullscreen } = useSidebar();

  useEffect(() => {
    if (isFullscreen) {
      document.body.style.overflow = "hidden";
      return () => {
        document.body.style.overflow = "auto";
      };
    }
  }, [isFullscreen]);

  if (isFullscreen) {
    return (
      <div className="fixed inset-0 z-50 bg-gray-950">
        <div className={cn("h-full w-full", className)}>
          {children}
        </div>
      </div>
    );
  }

  return <div className={className}>{children}</div>;
}
