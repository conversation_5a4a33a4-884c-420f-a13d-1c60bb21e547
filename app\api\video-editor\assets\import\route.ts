import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { fileStore } from "@/lib/file-store";

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const formData = await request.formData();
    const file = formData.get("file") as File;
    const projectId = formData.get("projectId") as string;
    const folderId = formData.get("folderId") as string;

    // Get metadata from form data if provided
    const duration = formData.get("duration")
      ? parseFloat(formData.get("duration") as string)
      : null;
    const width = formData.get("width")
      ? parseInt(formData.get("width") as string)
      : null;
    const height = formData.get("height")
      ? parseInt(formData.get("height") as string)
      : null;
    const fps = formData.get("fps")
      ? parseInt(formData.get("fps") as string)
      : null;
    const hasAudio = formData.get("hasAudio") === "true";

    if (!file) {
      return NextResponse.json({ error: "No file provided" }, { status: 400 });
    }

    // Generate a unique asset ID
    const assetId = `asset-${Date.now()}-${Math.random()
      .toString(36)
      .substr(2, 9)}`;

    // Store the file in memory (in production, use proper storage)
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    fileStore.set(assetId, {
      buffer,
      mimeType: file.type,
      filename: file.name,
      userId: session.user.id,
    });

    // Create a URL that points to our serve endpoint
    const url = `/api/video-editor/assets/serve/${assetId}`;

    // Determine asset type based on file type
    let assetType = "video";
    if (file.type.startsWith("audio/")) {
      assetType = "audio";
    } else if (file.type.startsWith("image/")) {
      assetType = "image";
    }

    // Use detected metadata or fallback to defaults
    let assetDuration = 0;
    let assetWidth = 0;
    let assetHeight = 0;
    let assetFps = 30;

    if (assetType === "video") {
      assetDuration = duration || 30; // Use detected duration or default
      assetWidth = width || 1920;
      assetHeight = height || 1080;
      assetFps = fps || 30;
    } else if (assetType === "audio") {
      assetDuration = duration || 180; // Use detected duration or default
    }

    const asset = {
      id: assetId,
      name: file.name,
      type: assetType,
      url: url,
      thumbnailUrl: assetType === "image" ? url : null,
      duration: assetDuration,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      projectId: projectId || null,
      folderId: folderId || null,
      metadata: {
        fileSize: file.size,
        width: assetWidth,
        height: assetHeight,
        fps: assetFps,
        format: file.name.split(".").pop()?.toLowerCase() || "unknown",
        originalName: file.name,
        mimeType: file.type,
      },
    };

    return NextResponse.json(asset);
  } catch (error) {
    console.error("Error importing asset:", error);
    return NextResponse.json(
      { error: "Failed to import asset" },
      { status: 500 }
    );
  }
}
