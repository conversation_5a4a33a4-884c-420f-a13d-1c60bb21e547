"use client";

import { CreditDisplay } from "@/components/credit-display";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useCreditStore } from "@/lib/stores/creditStore";
import { SidebarProvider, useSidebar } from "@/lib/contexts/sidebar-context";
import clsx from "clsx";
import {
  ImageIcon,
  Image as ImageLucide,
  LogOut,
  Menu,
  Scissors,
  Settings,
  Sparkles,
  Type,
  Users,
  Video,
  X,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import { signOut, useSession } from "next-auth/react";
import Image from "next/image";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { useEffect, useMemo } from "react";

function DashboardLayoutContent({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  const router = useRouter();
  const { data: session, status } = useSession({
    required: true,
    onUnauthenticated() {
      router.push(`/login?callbackUrl=${encodeURIComponent(pathname)}`);
    },
  });
  const {
    isCollapsed,
    isMobileSidebarOpen,
    setMobileSidebarOpen,
    toggleSidebar,
    isFullscreen,
  } = useSidebar();
  const fetchCredits = useCreditStore((state) => state.fetchCredits);

  // Initialize credit store when dashboard loads
  useEffect(() => {
    if (session?.user) {
      fetchCredits();
    }
  }, [session, fetchCredits]);

  // Close sidebar when route changes on mobile
  useEffect(() => {
    setMobileSidebarOpen(false);
  }, [pathname, setMobileSidebarOpen]);

  // Close sidebar when clicking outside on mobile
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const sidebar = document.getElementById("mobile-sidebar");
      if (
        sidebar &&
        !sidebar.contains(event.target as Node) &&
        isMobileSidebarOpen
      ) {
        setMobileSidebarOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isMobileSidebarOpen, setMobileSidebarOpen]);

  const handleSignOut = async () => {
    await signOut({ redirect: true, callbackUrl: "/" });
  };

  // Dynamic navigation items based on user type
  const navItems = useMemo(() => {
    const items = [
      { label: "Dashboard", path: "/dashboard", icon: Sparkles },
      { label: "Text to Video", path: "/dashboard/video", icon: Type },
      {
        label: "Text to Image",
        path: "/dashboard/text-to-image",
        icon: ImageLucide,
      },
      { label: "Image to Video", path: "/dashboard/image", icon: ImageIcon },
      { label: "Video Editor", path: "/dashboard/editor", icon: Scissors },
      { label: "My Videos", path: "/dashboard/myvideos", icon: Video },
      { label: "Settings", path: "/dashboard/settings", icon: Settings },
    ];

    // Add Users management for admin users
    if (session?.user?.userType === "admin") {
      items.splice(items.length - 1, 0, {
        label: "Users",
        path: "/dashboard/users",
        icon: Users,
      });
    }

    // Add User Access Control for agency users
    if (
      session?.user?.userType === "agency-basic" ||
      session?.user?.userType === "agency-deluxe"
    ) {
      items.splice(items.length - 1, 0, {
        label: "User Access",
        path: "/dashboard/user-access",
        icon: Users,
      });
    }

    return items;
  }, [session]);

  // Show loading state while checking authentication
  if (status === "loading") {
    return (
      <div className="flex h-screen items-center justify-center bg-gray-950">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
        <span className="ml-3 text-white">Loading...</span>
      </div>
    );
  }

  return (
    <div className="flex h-screen overflow-hidden bg-gray-950">
      {/* Mobile Header */}
      <div className="lg:hidden fixed top-0 left-0 right-0 z-20 bg-gray-900 border-b border-gray-800 flex items-center justify-between px-4 py-3">
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="icon"
            className="text-gray-300"
            onClick={() => setMobileSidebarOpen(!isMobileSidebarOpen)}
          >
            <Menu className="w-6 h-6" />
          </Button>
          <Link href="/" className="flex items-center">
            <Image
              src="/logo-w.png"
              alt="FLOWY AI Logo"
              width={64}
              height={64}
            />
          </Link>
        </div>
        <div className="flex items-center gap-3">
          <div className="hidden sm:block">
            <CreditDisplay variant="compact" />
          </div>
          <Avatar className="w-8 h-8">
            <AvatarImage src="/placeholder.svg" />
            <AvatarFallback className="bg-purple-600 text-white">
              {session?.user?.name?.charAt(0)}
              {session?.user?.name?.charAt(1)}
            </AvatarFallback>
          </Avatar>
        </div>
      </div>

      {/* Mobile Sidebar Overlay */}
      {isMobileSidebarOpen && (
        <div className="lg:hidden fixed inset-0 bg-black/50 z-30" />
      )}

      {/* Sidebar - Desktop (always visible) & Mobile (toggleable) */}
      <aside
        id="mobile-sidebar"
        className={clsx(
          "bg-gray-900 border-r border-gray-800 flex flex-col h-screen z-40 transition-all duration-300 ease-in-out",
          isFullscreen
            ? "hidden"
            : isCollapsed
            ? "lg:w-16 lg:sticky lg:top-0 lg:left-0"
            : "lg:w-64 lg:sticky lg:top-0 lg:left-0",
          "fixed top-0 left-0 w-64",
          isMobileSidebarOpen
            ? "translate-x-0"
            : "-translate-x-full lg:translate-x-0"
        )}
      >
        {/* Mobile Close Button */}
        <div className="lg:hidden flex justify-between items-center px-4 py-3 border-b border-gray-800">
          <Link href="/" className="flex items-center">
            <Image
              src="/logo-w.png"
              alt="FLOWY AI Logo"
              width={64}
              height={64}
            />
          </Link>
          <Button
            variant="ghost"
            size="icon"
            className="text-gray-300"
            onClick={() => setMobileSidebarOpen(false)}
          >
            <X className="w-5 h-5" />
          </Button>
        </div>

        {/* Desktop Logo - Hidden on Mobile */}
        <div className="hidden lg:flex items-center gap-2 px-6 py-5 border-b border-gray-800">
          <Link href="/" className="flex items-center gap-2">
            <div
              className={clsx(
                "transition-all duration-300",
                isCollapsed ? "ml-2" : "ml-5"
              )}
            >
              <Image
                src="/logo-w.png"
                alt="FLOWY AI Logo"
                width={isCollapsed ? 32 : 96}
                height={isCollapsed ? 32 : 96}
              />
            </div>
          </Link>
          {!isCollapsed && (
            <Button
              variant="ghost"
              size="icon"
              className="text-gray-300 ml-auto"
              onClick={toggleSidebar}
            >
              <ChevronLeft className="w-4 h-4" />
            </Button>
          )}
          {isCollapsed && (
            <Button
              variant="ghost"
              size="icon"
              className="text-gray-300 ml-auto"
              onClick={toggleSidebar}
            >
              <ChevronRight className="w-4 h-4" />
            </Button>
          )}
        </div>

        {/* Navigation - Scrollable if needed */}
        <nav className="flex-grow overflow-y-auto py-6 px-4 space-y-1">
          {navItems.map((item) => {
            const isActive =
              item.path === "/dashboard"
                ? pathname === "/dashboard"
                : pathname.startsWith(item.path);

            return (
              <Link
                key={item.path}
                href={item.path}
                className={clsx(
                  "flex items-center w-full px-3 py-2 rounded-lg text-sm font-medium transition-colors gap-3",
                  isActive
                    ? "bg-purple-700 text-white"
                    : "text-gray-300 hover:bg-gray-800 hover:text-white",
                  isCollapsed && "justify-center"
                )}
                title={isCollapsed ? item.label : undefined}
              >
                <item.icon className="w-5 h-5 flex-shrink-0" />
                {!isCollapsed && <span>{item.label}</span>}
              </Link>
            );
          })}
        </nav>

        {/* Credits display */}
        {!isCollapsed && (
          <div className="p-4 border-t border-gray-800">
            <CreditDisplay showProgress showReset />
          </div>
        )}

        {/* User profile - Fixed at bottom */}
        <div className="p-4 border-t border-gray-800 mt-auto">
          <div
            className={clsx(
              "flex items-center gap-3 mb-3",
              isCollapsed && "justify-center"
            )}
          >
            <Avatar className="w-9 h-9">
              <AvatarImage src="/placeholder.svg" />
              <AvatarFallback className="bg-purple-600 text-white">
                {session?.user?.name?.charAt(0)}
                {session?.user?.name?.charAt(1)}
              </AvatarFallback>
            </Avatar>
            {!isCollapsed && (
              <div>
                <p className="text-white font-medium text-sm">
                  {session?.user?.name}
                </p>
                <div className="flex items-center space-x-2">
                  <Badge className="bg-green-600 text-white text-xs">
                    {session?.user?.userType?.toUpperCase()}
                  </Badge>
                </div>
              </div>
            )}
          </div>
          <Button
            variant="outline"
            className={clsx(
              "border-gray-700 bg-purple-600 hover:bg-purple-700",
              isCollapsed ? "w-auto px-2" : "w-full"
            )}
            onClick={handleSignOut}
            title={isCollapsed ? "Sign Out" : undefined}
          >
            <LogOut className="w-4 h-4" />
            {!isCollapsed && <span className="ml-2">Sign Out</span>}
          </Button>
        </div>
      </aside>

      {/* Main Content - Scrollable */}
      <main
        className={clsx(
          "flex-1 overflow-y-auto bg-gray-950 lg:pt-0 pt-16 transition-all duration-300",
          isFullscreen ? "w-full" : ""
        )}
      >
        <div
          className={clsx(
            "w-full py-6 px-4",
            isFullscreen ? "max-w-none" : "max-w-4xl mx-auto"
          )}
        >
          {children}
        </div>
      </main>
    </div>
  );
}

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <SidebarProvider>
      <DashboardLayoutContent>{children}</DashboardLayoutContent>
    </SidebarProvider>
  );
}
