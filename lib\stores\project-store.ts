import type {
  ExportSettings,
  Project,
  ProjectSettings,
} from "@/types/video-editor";
import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";
import { AutoSaveService } from "@/lib/services/auto-save-service";
import type {
  SharePermissions,
  PublicShareLink,
} from "@/lib/services/project-sharing-service";
import type { VideoProjectVersion } from "@/lib/schema";

interface ProjectState {
  // Current project data
  currentProject: Project | null;
  projects: Project[];

  // UI state
  isLoading: boolean;
  isSaving: boolean;
  isExporting: boolean;
  exportProgress: number;
  lastSaved: Date | null;
  hasUnsavedChanges: boolean;

  // Auto-save settings
  autoSaveEnabled: boolean;
  autoSaveInterval: number; // in milliseconds
  autoSaveInstance: AutoSaveService | null;

  // Sharing state
  shareLinks: PublicShareLink[];
  isSharing: boolean;

  // Actions - Project management
  createProject: (
    name: string,
    description?: string,
    settings?: Partial<ProjectSettings>
  ) => Promise<Project>;
  loadProject: (id: string) => Promise<void>;
  saveProject: () => Promise<void>;
  deleteProject: (id: string) => Promise<void>;
  duplicateProject: (id: string) => Promise<Project>;

  // Actions - Project data
  updateProject: (
    updates: Partial<Omit<Project, "id" | "createdAt" | "updatedAt" | "userId">>
  ) => void;
  updateProjectSettings: (settings: Partial<ProjectSettings>) => void;
  updateProjectSettingsById: (
    id: string,
    settings: Partial<ProjectSettings>
  ) => Promise<void>;
  setCurrentProject: (project: Project | null) => void;

  // Actions - Project list management
  loadProjects: () => Promise<void>;
  setProjects: (projects: Project[]) => void;

  // Actions - Export functionality
  exportProject: (settings: ExportSettings) => Promise<string>;
  setExportProgress: (progress: number) => void;

  // Actions - Auto-save
  enableAutoSave: (interval?: number) => void;
  disableAutoSave: () => void;
  markAsChanged: () => void;
  markAsSaved: () => void;
  initializeAutoSave: (userId: string) => void;
  destroyAutoSave: () => void;

  // Actions - Version management
  createVersion: (name?: string) => Promise<void>;
  loadVersion: (versionId: string) => Promise<void>;
  getProjectVersions: () => Promise<VideoProjectVersion[]>;

  // Actions - Project sharing
  createShareLink: (
    permissions: SharePermissions,
    expiresInHours?: number
  ) => Promise<PublicShareLink>;
  shareWithUser: (
    email: string,
    permissions: SharePermissions
  ) => Promise<void>;
  revokeShare: (shareId: string) => Promise<void>;
  getProjectShares: () => Promise<PublicShareLink[]>;

  // Actions - Import/Export for backup
  exportProjectData: () => Promise<{
    project: Project;
    versions: VideoProjectVersion[];
  }>;
  importProjectData: (
    projectData: Project,
    newName?: string
  ) => Promise<Project>;

  // Utility functions
  getProjectById: (id: string) => Project | undefined;
  hasProject: (id: string) => boolean;
  getDefaultSettings: () => ProjectSettings;
}

const DEFAULT_PROJECT_SETTINGS: ProjectSettings = {
  width: 1920,
  height: 1080,
  fps: 30,
  duration: 60, // 60 seconds default
  backgroundColor: "#000000",
};

export const useProjectStore = create<ProjectState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        currentProject: null,
        projects: [],
        isLoading: false,
        isSaving: false,
        isExporting: false,
        exportProgress: 0,
        lastSaved: null,
        hasUnsavedChanges: false,
        autoSaveEnabled: true,
        autoSaveInterval: 30000, // 30 seconds
        autoSaveInstance: null,
        shareLinks: [],
        isSharing: false,

        // Project management
        createProject: async (name, description, settingsOverride) => {
          set({ isLoading: true });

          try {
            const settings = {
              ...DEFAULT_PROJECT_SETTINGS,
              ...settingsOverride,
            };

            // API call to create project
            const response = await fetch("/api/video-editor/projects", {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({
                name,
                description,
                settings,
              }),
            });

            if (!response.ok) {
              throw new Error("Failed to create project");
            }

            const createdProject = await response.json();

            set((state) => ({
              projects: [...state.projects, createdProject],
              currentProject: createdProject,
              isLoading: false,
              hasUnsavedChanges: false,
              lastSaved: new Date(),
            }));

            return createdProject;
          } catch (error) {
            set({ isLoading: false });
            throw error;
          }
        },

        loadProject: async (id) => {
          set({ isLoading: true });

          try {
            const response = await fetch(`/api/video-editor/projects/${id}`);

            if (!response.ok) {
              throw new Error("Failed to load project");
            }

            const project = await response.json();

            set({
              currentProject: project,
              isLoading: false,
              hasUnsavedChanges: false,
              lastSaved: new Date(project.updatedAt),
            });
          } catch (error) {
            set({ isLoading: false });
            throw error;
          }
        },

        saveProject: async () => {
          const state = get();
          if (!state.currentProject || state.isSaving) return;

          set({ isSaving: true });

          try {
            const response = await fetch(
              `/api/video-editor/projects/${state.currentProject.id}`,
              {
                method: "PUT",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify(state.currentProject),
              }
            );

            if (!response.ok) {
              throw new Error("Failed to save project");
            }

            const updatedProject = await response.json();

            set((state) => ({
              currentProject: updatedProject,
              projects: state.projects.map((p) =>
                p.id === updatedProject.id ? updatedProject : p
              ),
              isSaving: false,
              hasUnsavedChanges: false,
              lastSaved: new Date(),
            }));
          } catch (error) {
            set({ isSaving: false });
            throw error;
          }
        },

        deleteProject: async (id) => {
          set({ isLoading: true });

          try {
            const response = await fetch(`/api/video-editor/projects/${id}`, {
              method: "DELETE",
            });

            if (!response.ok) {
              throw new Error("Failed to delete project");
            }

            set((state) => ({
              projects: state.projects.filter((p) => p.id !== id),
              currentProject:
                state.currentProject?.id === id ? null : state.currentProject,
              isLoading: false,
            }));
          } catch (error) {
            set({ isLoading: false });
            throw error;
          }
        },

        duplicateProject: async (id) => {
          const state = get();
          const originalProject = state.projects.find((p) => p.id === id);

          if (!originalProject) {
            throw new Error("Project not found");
          }

          return get().createProject(
            `${originalProject.name} Copy`,
            originalProject.description,
            originalProject.settings
          );
        },

        // Project data updates
        updateProject: (updates) =>
          set((state) => {
            if (!state.currentProject) return state;

            const updatedProject = {
              ...state.currentProject,
              ...updates,
              updatedAt: new Date(),
            };

            return {
              currentProject: updatedProject,
              projects: state.projects.map((p) =>
                p.id === updatedProject.id ? updatedProject : p
              ),
              hasUnsavedChanges: true,
            };
          }),

        updateProjectSettings: (settings) =>
          set((state) => {
            if (!state.currentProject) return state;

            const updatedProject = {
              ...state.currentProject,
              settings: { ...state.currentProject.settings, ...settings },
              updatedAt: new Date(),
            };

            return {
              currentProject: updatedProject,
              projects: state.projects.map((p) =>
                p.id === updatedProject.id ? updatedProject : p
              ),
              hasUnsavedChanges: true,
            };
          }),

        updateProjectSettingsById: async (id, settings) => {
          const state = get();
          const project = state.projects.find((p) => p.id === id);

          if (!project) {
            throw new Error("Project not found");
          }

          const updatedProject = {
            ...project,
            settings: { ...project.settings, ...settings },
            updatedAt: new Date(),
          };

          set((state) => ({
            currentProject:
              state.currentProject?.id === id
                ? updatedProject
                : state.currentProject,
            projects: state.projects.map((p) =>
              p.id === id ? updatedProject : p
            ),
            hasUnsavedChanges: true,
          }));

          // Auto-save the changes
          await get().saveProject();
        },

        setCurrentProject: (project) =>
          set({
            currentProject: project,
            hasUnsavedChanges: false,
            lastSaved: project ? new Date(project.updatedAt) : null,
          }),

        // Project list management
        loadProjects: async () => {
          set({ isLoading: true });

          try {
            const response = await fetch("/api/video-editor/projects");

            if (!response.ok) {
              throw new Error("Failed to load projects");
            }

            const projects = await response.json();

            set({
              projects,
              isLoading: false,
            });
          } catch (error) {
            set({ isLoading: false });
            throw error;
          }
        },

        setProjects: (projects) => set({ projects }),

        // Export functionality
        exportProject: async (settings) => {
          const state = get();
          if (!state.currentProject) {
            throw new Error("No project to export");
          }

          set({ isExporting: true, exportProgress: 0 });

          try {
            const response = await fetch("/api/video-editor/export", {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({
                projectId: state.currentProject.id,
                settings,
              }),
            });

            if (!response.ok) {
              throw new Error("Failed to start export");
            }

            const { exportId } = await response.json();

            // Poll for export progress
            const pollProgress = async (): Promise<string> => {
              const progressResponse = await fetch(
                `/api/video-editor/export/${exportId}`
              );
              const progressData = await progressResponse.json();

              set({ exportProgress: progressData.progress });

              if (progressData.status === "completed") {
                set({ isExporting: false, exportProgress: 100 });
                return progressData.outputUrl;
              } else if (progressData.status === "failed") {
                set({ isExporting: false, exportProgress: 0 });
                throw new Error(progressData.errorMessage || "Export failed");
              }

              // Continue polling
              await new Promise((resolve) => setTimeout(resolve, 1000));
              return pollProgress();
            };

            return await pollProgress();
          } catch (error) {
            set({ isExporting: false, exportProgress: 0 });
            throw error;
          }
        },

        setExportProgress: (progress) => set({ exportProgress: progress }),

        // Auto-save functionality
        enableAutoSave: (interval = 30000) =>
          set((state) => {
            if (state.autoSaveInstance) {
              state.autoSaveInstance.updateConfig({ interval });
              state.autoSaveInstance.start();
            }
            return {
              autoSaveEnabled: true,
              autoSaveInterval: interval,
            };
          }),

        disableAutoSave: () =>
          set((state) => {
            if (state.autoSaveInstance) {
              state.autoSaveInstance.stop();
            }
            return { autoSaveEnabled: false };
          }),

        markAsChanged: () =>
          set((state) => {
            if (state.autoSaveInstance) {
              state.autoSaveInstance.markDirty();
            }
            return { hasUnsavedChanges: true };
          }),

        markAsSaved: () =>
          set({
            hasUnsavedChanges: false,
            lastSaved: new Date(),
          }),

        initializeAutoSave: (userId) =>
          set((state) => {
            if (state.currentProject && !state.autoSaveInstance) {
              const autoSave = AutoSaveService.getInstance(
                state.currentProject.id,
                userId,
                { interval: state.autoSaveInterval }
              );

              // Set up callbacks
              autoSave.onSave((success, error) => {
                if (success) {
                  set({ hasUnsavedChanges: false, lastSaved: new Date() });
                } else {
                  console.error("Auto-save failed:", error);
                }
              });

              if (state.autoSaveEnabled) {
                autoSave.start();
              }

              return { autoSaveInstance: autoSave };
            }
            return state;
          }),

        destroyAutoSave: () =>
          set((state) => {
            if (state.autoSaveInstance) {
              state.autoSaveInstance.destroy();
            }
            return { autoSaveInstance: null };
          }),

        // Version management
        createVersion: async (name) => {
          const state = get();
          if (!state.currentProject) return;

          try {
            const response = await fetch("/api/video-editor/versions", {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({
                projectId: state.currentProject.id,
                name,
                timeline: state.currentProject.timeline,
                settings: state.currentProject.settings,
              }),
            });

            if (!response.ok) {
              throw new Error("Failed to create version");
            }
          } catch (error) {
            throw error;
          }
        },

        loadVersion: async (versionId) => {
          try {
            const response = await fetch(
              `/api/video-editor/versions/${versionId}`
            );

            if (!response.ok) {
              throw new Error("Failed to load version");
            }

            const version = await response.json();

            set((state) => {
              if (!state.currentProject) return state;

              const updatedProject = {
                ...state.currentProject,
                timeline: version.timeline,
                settings: version.settings,
                updatedAt: new Date(),
              };

              return {
                currentProject: updatedProject,
                hasUnsavedChanges: true,
              };
            });
          } catch (error) {
            throw error;
          }
        },

        // Utility functions
        getProjectById: (id) => {
          const state = get();
          return state.projects.find((project) => project.id === id);
        },

        hasProject: (id) => {
          const state = get();
          return state.projects.some((project) => project.id === id);
        },

        getDefaultSettings: () => ({ ...DEFAULT_PROJECT_SETTINGS }),

        // Version management - additional methods
        getProjectVersions: async () => {
          const state = get();
          if (!state.currentProject) return [];

          try {
            const response = await fetch(
              `/api/video-editor/projects/${state.currentProject.id}/versions`
            );

            if (!response.ok) {
              throw new Error("Failed to load versions");
            }

            return await response.json();
          } catch (error) {
            console.error("Error loading versions:", error);
            return [];
          }
        },

        // Project sharing
        createShareLink: async (permissions, expiresInHours) => {
          const state = get();
          if (!state.currentProject) {
            throw new Error("No project to share");
          }

          set({ isSharing: true });

          try {
            const response = await fetch("/api/video-editor/share", {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({
                projectId: state.currentProject.id,
                permissions,
                expiresInHours,
              }),
            });

            if (!response.ok) {
              throw new Error("Failed to create share link");
            }

            const shareLink = await response.json();

            set((state) => ({
              shareLinks: [...state.shareLinks, shareLink],
              isSharing: false,
            }));

            return shareLink;
          } catch (error) {
            set({ isSharing: false });
            throw error;
          }
        },

        shareWithUser: async (email, permissions) => {
          const state = get();
          if (!state.currentProject) {
            throw new Error("No project to share");
          }

          set({ isSharing: true });

          try {
            const response = await fetch("/api/video-editor/share/user", {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({
                projectId: state.currentProject.id,
                email,
                permissions,
              }),
            });

            if (!response.ok) {
              throw new Error("Failed to share with user");
            }

            set({ isSharing: false });
          } catch (error) {
            set({ isSharing: false });
            throw error;
          }
        },

        revokeShare: async (shareId) => {
          try {
            const response = await fetch(`/api/video-editor/share/${shareId}`, {
              method: "DELETE",
            });

            if (!response.ok) {
              throw new Error("Failed to revoke share");
            }

            set((state) => ({
              shareLinks: state.shareLinks.filter(
                (link) => link.token !== shareId
              ),
            }));
          } catch (error) {
            throw error;
          }
        },

        getProjectShares: async () => {
          const state = get();
          if (!state.currentProject) return [];

          try {
            const response = await fetch(
              `/api/video-editor/projects/${state.currentProject.id}/shares`
            );

            if (!response.ok) {
              throw new Error("Failed to load shares");
            }

            return await response.json();
          } catch (error) {
            console.error("Error loading shares:", error);
            return [];
          }
        },

        // Import/Export for backup
        exportProjectData: async () => {
          const state = get();
          if (!state.currentProject) {
            throw new Error("No project to export");
          }

          try {
            const response = await fetch(
              `/api/video-editor/projects/${state.currentProject.id}/export-data`
            );

            if (!response.ok) {
              throw new Error("Failed to export project data");
            }

            return await response.json();
          } catch (error) {
            throw error;
          }
        },

        importProjectData: async (projectData, newName) => {
          set({ isLoading: true });

          try {
            const response = await fetch("/api/video-editor/projects/import", {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({
                projectData,
                newName,
              }),
            });

            if (!response.ok) {
              throw new Error("Failed to import project");
            }

            const importedProject = await response.json();

            set((state) => ({
              projects: [...state.projects, importedProject],
              isLoading: false,
            }));

            return importedProject;
          } catch (error) {
            set({ isLoading: false });
            throw error;
          }
        },
      }),
      {
        name: "project-store",
        partialize: (state) => ({
          projects: state.projects,
          autoSaveEnabled: state.autoSaveEnabled,
          autoSaveInterval: state.autoSaveInterval,
        }),
      }
    )
  )
);
