// Shared file store for uploaded assets
// In production, this should be replaced with a proper database or cloud storage solution

interface FileData {
  buffer: Buffer;
  mimeType: string;
  filename: string;
  userId: string;
}

class FileStore {
  private store = new Map<string, FileData>();

  set(id: string, data: FileData): void {
    this.store.set(id, data);
  }

  get(id: string): FileData | undefined {
    return this.store.get(id);
  }

  delete(id: string): boolean {
    return this.store.delete(id);
  }

  has(id: string): boolean {
    return this.store.has(id);
  }

  // Get files for a specific user
  getUserFiles(userId: string): Array<{ id: string; data: FileData }> {
    const userFiles: Array<{ id: string; data: FileData }> = [];
    for (const [id, data] of this.store.entries()) {
      if (data.userId === userId) {
        userFiles.push({ id, data });
      }
    }
    return userFiles;
  }

  // Clean up old files (optional, for memory management)
  cleanup(): void {
    // This is a simple implementation - in production you'd want proper cleanup logic
    // For now, we'll keep all files since we don't have timestamps
  }
}

export const fileStore = new FileStore();
