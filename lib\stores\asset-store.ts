import type { AISettings, Asset, Folder } from "@/types/video-editor";
import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";
import { getVideoMetadata, getAudioMetadata } from "@/lib/utils/video-metadata";

interface AssetState {
  // Asset data
  assets: Asset[];
  folders: Folder[];
  selectedAssets: string[];

  // UI state
  isLoading: boolean;
  isImporting: boolean;
  isGenerating: boolean;
  generationProgress: number;
  currentFolderId: string | null;
  searchQuery: string;
  filterType: "all" | "video" | "audio" | "image" | "ai-generated";
  sortBy: "name" | "date" | "type" | "size";
  sortOrder: "asc" | "desc";

  // Actions - Asset management
  loadAssets: (projectId?: string) => Promise<void>;
  importAsset: (
    file: File,
    projectId?: string,
    folderId?: string
  ) => Promise<Asset>;
  deleteAsset: (id: string) => Promise<void>;
  updateAsset: (id: string, updates: Partial<Asset>) => Promise<void>;
  duplicateAsset: (id: string) => Promise<Asset>;

  // Actions - AI generation
  generateAIVideo: (
    prompt: string,
    settings: AISettings,
    projectId?: string
  ) => Promise<Asset>;
  setGenerationProgress: (progress: number) => void;
  setGenerating: (isGenerating: boolean) => void;

  // Actions - Folder management
  createFolder: (
    name: string,
    parentId?: string,
    projectId?: string
  ) => Promise<Folder>;
  deleteFolder: (id: string) => Promise<void>;
  updateFolder: (id: string, updates: Partial<Folder>) => Promise<void>;
  moveAssetToFolder: (
    assetId: string,
    folderId: string | null
  ) => Promise<void>;

  // Actions - Selection and organization
  selectAsset: (id: string, multiSelect?: boolean) => void;
  selectAssets: (ids: string[]) => void;
  clearSelection: () => void;
  organizeAssets: (
    assetIds: string[],
    folderId: string | null
  ) => Promise<void>;

  // Actions - Navigation and filtering
  setCurrentFolder: (folderId: string | null) => void;
  setSearchQuery: (query: string) => void;
  setFilterType: (
    type: "all" | "video" | "audio" | "image" | "ai-generated"
  ) => void;
  setSorting: (
    sortBy: "name" | "date" | "type" | "size",
    order: "asc" | "desc"
  ) => void;

  // Utility functions
  getAssetById: (id: string) => Asset | undefined;
  getFolderById: (id: string) => Folder | undefined;
  getFilteredAssets: () => Asset[];
  getAssetsByFolder: (folderId: string | null) => Asset[];
  getFolderHierarchy: (folderId: string) => Folder[];
  getSelectedAssets: () => Asset[];
  getTotalAssetSize: () => number;
}

export const useAssetStore = create<AssetState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        assets: [],
        folders: [],
        selectedAssets: [],
        isLoading: false,
        isImporting: false,
        isGenerating: false,
        generationProgress: 0,
        currentFolderId: null,
        searchQuery: "",
        filterType: "all",
        sortBy: "date",
        sortOrder: "desc",

        // Asset management
        loadAssets: async (projectId) => {
          set({ isLoading: true });

          try {
            const url = projectId
              ? `/api/video-editor/assets?projectId=${projectId}`
              : "/api/video-editor/assets";

            const response = await fetch(url);

            if (!response.ok) {
              throw new Error("Failed to load assets");
            }

            const data = await response.json();

            set({
              assets: data.assets || [],
              folders: data.folders || [],
              isLoading: false,
            });
          } catch (error) {
            set({ isLoading: false });
            throw error;
          }
        },

        importAsset: async (file, projectId, folderId) => {
          set({ isImporting: true });

          try {
            let metadata = {
              fileSize: file.size,
              mimeType: file.type,
            };

            // Detect metadata for video and audio files
            if (file.type.startsWith("video/")) {
              try {
                const videoMetadata = await getVideoMetadata(file);
                metadata = {
                  ...metadata,
                  width: videoMetadata.width,
                  height: videoMetadata.height,
                  fps: videoMetadata.fps,
                };

                // Add detected metadata to form data
                const formData = new FormData();
                formData.append("file", file);
                formData.append("duration", videoMetadata.duration.toString());
                formData.append("width", videoMetadata.width.toString());
                formData.append("height", videoMetadata.height.toString());
                formData.append("fps", videoMetadata.fps.toString());
                formData.append("hasAudio", videoMetadata.hasAudio.toString());
                if (projectId) formData.append("projectId", projectId);
                if (folderId) formData.append("folderId", folderId);

                const response = await fetch(
                  "/api/video-editor/assets/import",
                  {
                    method: "POST",
                    body: formData,
                  }
                );

                if (!response.ok) {
                  throw new Error("Failed to import asset");
                }

                const asset = await response.json();

                set((state) => ({
                  assets: [...state.assets, asset],
                  isImporting: false,
                }));

                return asset;
              } catch (metadataError) {
                console.warn("Failed to detect video metadata:", metadataError);
                // Fall back to regular upload without metadata
              }
            } else if (file.type.startsWith("audio/")) {
              try {
                const audioMetadata = await getAudioMetadata(file);

                const formData = new FormData();
                formData.append("file", file);
                formData.append(
                  "duration",
                  audioMetadata.duration?.toString() || "0"
                );
                if (projectId) formData.append("projectId", projectId);
                if (folderId) formData.append("folderId", folderId);

                const response = await fetch(
                  "/api/video-editor/assets/import",
                  {
                    method: "POST",
                    body: formData,
                  }
                );

                if (!response.ok) {
                  throw new Error("Failed to import asset");
                }

                const asset = await response.json();

                set((state) => ({
                  assets: [...state.assets, asset],
                  isImporting: false,
                }));

                return asset;
              } catch (metadataError) {
                console.warn("Failed to detect audio metadata:", metadataError);
              }
            }

            // Fallback for other file types or if metadata detection fails
            const formData = new FormData();
            formData.append("file", file);
            if (projectId) formData.append("projectId", projectId);
            if (folderId) formData.append("folderId", folderId);

            const response = await fetch("/api/video-editor/assets/import", {
              method: "POST",
              body: formData,
            });

            if (!response.ok) {
              throw new Error("Failed to import asset");
            }

            const asset = await response.json();

            set((state) => ({
              assets: [...state.assets, asset],
              isImporting: false,
            }));

            return asset;
          } catch (error) {
            set({ isImporting: false });
            throw error;
          }
        },

        deleteAsset: async (id) => {
          try {
            const response = await fetch(`/api/video-editor/assets/${id}`, {
              method: "DELETE",
            });

            if (!response.ok) {
              throw new Error("Failed to delete asset");
            }

            set((state) => ({
              assets: state.assets.filter((asset) => asset.id !== id),
              selectedAssets: state.selectedAssets.filter(
                (assetId) => assetId !== id
              ),
            }));
          } catch (error) {
            throw error;
          }
        },

        updateAsset: async (id, updates) => {
          try {
            const response = await fetch(`/api/video-editor/assets/${id}`, {
              method: "PUT",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify(updates),
            });

            if (!response.ok) {
              throw new Error("Failed to update asset");
            }

            const updatedAsset = await response.json();

            set((state) => ({
              assets: state.assets.map((asset) =>
                asset.id === id ? updatedAsset : asset
              ),
            }));
          } catch (error) {
            throw error;
          }
        },

        duplicateAsset: async (id) => {
          const state = get();
          const originalAsset = state.assets.find((asset) => asset.id === id);

          if (!originalAsset) {
            throw new Error("Asset not found");
          }

          try {
            const response = await fetch("/api/video-editor/assets/duplicate", {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({ assetId: id }),
            });

            if (!response.ok) {
              throw new Error("Failed to duplicate asset");
            }

            const duplicatedAsset = await response.json();

            set((state) => ({
              assets: [...state.assets, duplicatedAsset],
            }));

            return duplicatedAsset;
          } catch (error) {
            throw error;
          }
        },

        // AI generation
        generateAIVideo: async (prompt, settings, projectId) => {
          set({ isGenerating: true, generationProgress: 0 });

          try {
            const response = await fetch("/api/video-editor/assets/generate", {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({
                prompt,
                settings,
                projectId,
              }),
            });

            if (!response.ok) {
              throw new Error("Failed to start AI generation");
            }

            const { generationId } = await response.json();

            // Poll for generation progress
            const pollProgress = async (): Promise<Asset> => {
              const progressResponse = await fetch(
                `/api/video-editor/assets/generate/${generationId}`
              );
              const progressData = await progressResponse.json();

              set({ generationProgress: progressData.progress });

              if (progressData.status === "completed") {
                const asset = progressData.asset;
                set((state) => ({
                  assets: [...state.assets, asset],
                  isGenerating: false,
                  generationProgress: 100,
                }));
                return asset;
              } else if (progressData.status === "failed") {
                set({ isGenerating: false, generationProgress: 0 });
                throw new Error(
                  progressData.errorMessage || "AI generation failed"
                );
              }

              // Continue polling
              await new Promise((resolve) => setTimeout(resolve, 2000));
              return pollProgress();
            };

            return await pollProgress();
          } catch (error) {
            set({ isGenerating: false, generationProgress: 0 });
            throw error;
          }
        },

        setGenerationProgress: (progress) =>
          set({ generationProgress: progress }),

        setGenerating: (isGenerating) => set({ isGenerating }),

        // Folder management
        createFolder: async (name, parentId, projectId) => {
          try {
            const response = await fetch("/api/video-editor/folders", {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({
                name,
                parentId,
                projectId,
              }),
            });

            if (!response.ok) {
              throw new Error("Failed to create folder");
            }

            const folder = await response.json();

            set((state) => ({
              folders: [...state.folders, folder],
            }));

            return folder;
          } catch (error) {
            throw error;
          }
        },

        deleteFolder: async (id) => {
          try {
            const response = await fetch(`/api/video-editor/folders/${id}`, {
              method: "DELETE",
            });

            if (!response.ok) {
              throw new Error("Failed to delete folder");
            }

            set((state) => ({
              folders: state.folders.filter((folder) => folder.id !== id),
              currentFolderId:
                state.currentFolderId === id ? null : state.currentFolderId,
            }));
          } catch (error) {
            throw error;
          }
        },

        updateFolder: async (id, updates) => {
          try {
            const response = await fetch(`/api/video-editor/folders/${id}`, {
              method: "PUT",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify(updates),
            });

            if (!response.ok) {
              throw new Error("Failed to update folder");
            }

            const updatedFolder = await response.json();

            set((state) => ({
              folders: state.folders.map((folder) =>
                folder.id === id ? updatedFolder : folder
              ),
            }));
          } catch (error) {
            throw error;
          }
        },

        moveAssetToFolder: async (assetId, folderId) => {
          try {
            const response = await fetch(
              `/api/video-editor/assets/${assetId}/move`,
              {
                method: "PUT",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ folderId }),
              }
            );

            if (!response.ok) {
              throw new Error("Failed to move asset");
            }

            const updatedAsset = await response.json();

            set((state) => ({
              assets: state.assets.map((asset) =>
                asset.id === assetId ? updatedAsset : asset
              ),
            }));
          } catch (error) {
            throw error;
          }
        },

        // Selection and organization
        selectAsset: (id, multiSelect = false) =>
          set((state) => ({
            selectedAssets: multiSelect
              ? state.selectedAssets.includes(id)
                ? state.selectedAssets.filter((assetId) => assetId !== id)
                : [...state.selectedAssets, id]
              : [id],
          })),

        selectAssets: (ids) => set({ selectedAssets: ids }),

        clearSelection: () => set({ selectedAssets: [] }),

        organizeAssets: async (assetIds, folderId) => {
          try {
            const promises = assetIds.map((assetId) =>
              get().moveAssetToFolder(assetId, folderId)
            );

            await Promise.all(promises);
          } catch (error) {
            throw error;
          }
        },

        // Navigation and filtering
        setCurrentFolder: (folderId) => set({ currentFolderId: folderId }),

        setSearchQuery: (query) => set({ searchQuery: query }),

        setFilterType: (type) => set({ filterType: type }),

        setSorting: (sortBy, order) => set({ sortBy, sortOrder: order }),

        // Utility functions
        getAssetById: (id) => {
          const state = get();
          return state.assets.find((asset) => asset.id === id);
        },

        getFolderById: (id) => {
          const state = get();
          return state.folders.find((folder) => folder.id === id);
        },

        getFilteredAssets: () => {
          const state = get();
          let filtered = state.assets;

          // Filter by current folder
          if (state.currentFolderId) {
            filtered = filtered.filter(
              (asset) => asset.folderId === state.currentFolderId
            );
          } else {
            filtered = filtered.filter((asset) => !asset.folderId);
          }

          // Filter by type
          if (state.filterType !== "all") {
            filtered = filtered.filter(
              (asset) => asset.type === state.filterType
            );
          }

          // Filter by search query
          if (state.searchQuery) {
            const query = state.searchQuery.toLowerCase();
            filtered = filtered.filter(
              (asset) =>
                asset.name.toLowerCase().includes(query) ||
                (asset.metadata.aiPrompt &&
                  asset.metadata.aiPrompt.toLowerCase().includes(query))
            );
          }

          // Sort assets
          filtered.sort((a, b) => {
            let comparison = 0;

            switch (state.sortBy) {
              case "name":
                comparison = a.name.localeCompare(b.name);
                break;
              case "date":
                comparison =
                  new Date(a.createdAt).getTime() -
                  new Date(b.createdAt).getTime();
                break;
              case "type":
                comparison = a.type.localeCompare(b.type);
                break;
              case "size":
                comparison = a.metadata.fileSize - b.metadata.fileSize;
                break;
            }

            return state.sortOrder === "asc" ? comparison : -comparison;
          });

          return filtered;
        },

        getAssetsByFolder: (folderId) => {
          const state = get();
          return state.assets.filter((asset) => asset.folderId === folderId);
        },

        getFolderHierarchy: (folderId) => {
          const state = get();
          const hierarchy: Folder[] = [];
          let currentFolder = state.folders.find((f) => f.id === folderId);

          while (currentFolder) {
            hierarchy.unshift(currentFolder);
            currentFolder = currentFolder.parentId
              ? state.folders.find((f) => f.id === currentFolder!.parentId)
              : undefined;
          }

          return hierarchy;
        },

        getSelectedAssets: () => {
          const state = get();
          return state.assets.filter((asset) =>
            state.selectedAssets.includes(asset.id)
          );
        },

        getTotalAssetSize: () => {
          const state = get();
          return state.assets.reduce(
            (total, asset) => total + asset.metadata.fileSize,
            0
          );
        },
      }),
      {
        name: "asset-store",
        partialize: (state) => ({
          searchQuery: state.searchQuery,
          filterType: state.filterType,
          sortBy: state.sortBy,
          sortOrder: state.sortOrder,
          currentFolderId: state.currentFolderId,
        }),
      }
    )
  )
);
