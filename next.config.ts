import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  async headers() {
    return [
      {
        source: "/:path*",
        headers: [
          {
            key: "Content-Security-Policy",
            value:
              "script-src 'self' 'unsafe-eval' 'unsafe-inline' va.vercel-scripts.com; media-src 'self' blob: data:",
          },
        ],
      },
    ];
  },
  // webpack: (config) => {
  //   // Handle esbuild modules that cause issues with Turbopack
  //   config.module.rules.push({
  //     test: /\.md$|\.exe$/,
  //     use: 'ignore-loader',
  //     include: /node_modules\/@remotion\/bundler\/node_modules\/@esbuild/,
  //   });

  //   return config;
  // },
};

export default nextConfig;
