"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Settings, Zap, AlertTriangle } from "lucide-react";
import { useProjectStore } from "@/lib/stores/project-store";
import { useAssetStore } from "@/lib/stores/asset-store";
import { suggestProjectSettings } from "@/lib/utils/video-metadata";
import type { ProjectSettings } from "@/types/video-editor";

interface ProjectSettingsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  showOptimizationSuggestion?: boolean;
}

const FPS_OPTIONS = [
  { value: 24, label: "24 FPS (Cinema)" },
  { value: 30, label: "30 FPS (Standard)" },
  { value: 50, label: "50 FPS (PAL)" },
  { value: 60, label: "60 FPS (Smooth)" },
  { value: 120, label: "120 FPS (High Speed)" },
];

const RESOLUTION_PRESETS = [
  { name: "HD", width: 1280, height: 720 },
  { name: "Full HD", width: 1920, height: 1080 },
  { name: "2K", width: 2560, height: 1440 },
  { name: "4K", width: 3840, height: 2160 },
  { name: "Instagram Square", width: 1080, height: 1080 },
  { name: "Instagram Story", width: 1080, height: 1920 },
  { name: "TikTok", width: 1080, height: 1920 },
  { name: "YouTube Shorts", width: 1080, height: 1920 },
];

export function ProjectSettingsDialog({
  open,
  onOpenChange,
  showOptimizationSuggestion = false,
}: ProjectSettingsDialogProps) {
  const projectStore = useProjectStore();
  const assetStore = useAssetStore();
  
  const [settings, setSettings] = useState<ProjectSettings>({
    width: 1920,
    height: 1080,
    fps: 30,
    duration: 60,
    backgroundColor: "#000000",
  });
  
  const [suggestedSettings, setSuggestedSettings] = useState<ProjectSettings | null>(null);
  const [showSuggestion, setShowSuggestion] = useState(showOptimizationSuggestion);

  useEffect(() => {
    if (projectStore.currentProject) {
      setSettings(projectStore.currentProject.settings);
    }
  }, [projectStore.currentProject]);

  useEffect(() => {
    if (showOptimizationSuggestion && assetStore.assets.length > 0) {
      const suggested = suggestProjectSettings(assetStore.assets.map(asset => ({
        width: asset.metadata.width,
        height: asset.metadata.height,
        fps: asset.metadata.fps,
        type: asset.type,
      })));
      
      setSuggestedSettings({
        ...settings,
        ...suggested,
      });
      setShowSuggestion(true);
    }
  }, [showOptimizationSuggestion, assetStore.assets, settings]);

  const handleSave = async () => {
    if (projectStore.currentProject) {
      await projectStore.updateProjectSettings(projectStore.currentProject.id, settings);
      onOpenChange(false);
    }
  };

  const applySuggestedSettings = () => {
    if (suggestedSettings) {
      setSettings(suggestedSettings);
      setShowSuggestion(false);
    }
  };

  const applyResolutionPreset = (preset: typeof RESOLUTION_PRESETS[0]) => {
    setSettings({
      ...settings,
      width: preset.width,
      height: preset.height,
    });
  };

  const currentResolutionName = RESOLUTION_PRESETS.find(
    preset => preset.width === settings.width && preset.height === settings.height
  )?.name;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="w-5 h-5" />
            Project Settings
          </DialogTitle>
          <DialogDescription>
            Configure your project's video settings. These affect playback and export quality.
          </DialogDescription>
        </DialogHeader>

        {showSuggestion && suggestedSettings && (
          <div className="bg-blue-50 dark:bg-blue-950 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-4">
            <div className="flex items-start gap-3">
              <Zap className="w-5 h-5 text-blue-600 mt-0.5" />
              <div className="flex-1">
                <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-1">
                  Optimization Suggestion
                </h4>
                <p className="text-sm text-blue-700 dark:text-blue-300 mb-3">
                  Based on your imported videos, we recommend these settings for optimal playback:
                </p>
                <div className="flex flex-wrap gap-2 mb-3">
                  <Badge variant="secondary">
                    {suggestedSettings.width}×{suggestedSettings.height}
                  </Badge>
                  <Badge variant="secondary">
                    {suggestedSettings.fps} FPS
                  </Badge>
                </div>
                <Button
                  size="sm"
                  onClick={applySuggestedSettings}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  Apply Suggested Settings
                </Button>
              </div>
            </div>
          </div>
        )}

        <div className="space-y-6">
          {/* Resolution Settings */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label className="text-base font-medium">Resolution</Label>
              {currentResolutionName && (
                <Badge variant="outline">{currentResolutionName}</Badge>
              )}
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="width">Width</Label>
                <Input
                  id="width"
                  type="number"
                  value={settings.width}
                  onChange={(e) =>
                    setSettings({
                      ...settings,
                      width: parseInt(e.target.value) || 1920,
                    })
                  }
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="height">Height</Label>
                <Input
                  id="height"
                  type="number"
                  value={settings.height}
                  onChange={(e) =>
                    setSettings({
                      ...settings,
                      height: parseInt(e.target.value) || 1080,
                    })
                  }
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label>Quick Presets</Label>
              <div className="grid grid-cols-2 sm:grid-cols-4 gap-2">
                {RESOLUTION_PRESETS.map((preset) => (
                  <Button
                    key={preset.name}
                    variant="outline"
                    size="sm"
                    onClick={() => applyResolutionPreset(preset)}
                    className="text-xs"
                  >
                    {preset.name}
                  </Button>
                ))}
              </div>
            </div>
          </div>

          {/* Frame Rate */}
          <div className="space-y-2">
            <Label htmlFor="fps">Frame Rate (FPS)</Label>
            <Select
              value={settings.fps.toString()}
              onValueChange={(value) =>
                setSettings({
                  ...settings,
                  fps: parseInt(value),
                })
              }
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {FPS_OPTIONS.map((option) => (
                  <SelectItem key={option.value} value={option.value.toString()}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <p className="text-sm text-gray-500">
              Higher frame rates provide smoother motion but larger file sizes.
            </p>
          </div>

          {/* Background Color */}
          <div className="space-y-2">
            <Label htmlFor="backgroundColor">Background Color</Label>
            <div className="flex gap-2">
              <Input
                id="backgroundColor"
                type="color"
                value={settings.backgroundColor}
                onChange={(e) =>
                  setSettings({
                    ...settings,
                    backgroundColor: e.target.value,
                  })
                }
                className="w-16 h-10"
              />
              <Input
                type="text"
                value={settings.backgroundColor}
                onChange={(e) =>
                  setSettings({
                    ...settings,
                    backgroundColor: e.target.value,
                  })
                }
                placeholder="#000000"
                className="flex-1"
              />
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSave}>
            Save Settings
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
