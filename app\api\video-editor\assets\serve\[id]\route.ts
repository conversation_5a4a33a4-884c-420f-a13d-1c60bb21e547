import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { fileStore } from "@/lib/file-store";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const assetId = (await params).id;
    const fileData = fileStore.get(assetId);

    if (!fileData) {
      return NextResponse.json({ error: "File not found" }, { status: 404 });
    }

    // Check if the user owns this file
    if (fileData.userId !== session.user.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
    }

    return new NextResponse(fileData.buffer, {
      headers: {
        "Content-Type": fileData.mimeType,
        "Cache-Control": "public, max-age=31536000",
        "Content-Disposition": `inline; filename="${fileData.filename}"`,
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET",
        "Access-Control-Allow-Headers": "Content-Type",
      },
    });
  } catch (error) {
    console.error("Error serving file:", error);
    return NextResponse.json(
      { error: "Failed to serve file" },
      { status: 500 }
    );
  }
}
