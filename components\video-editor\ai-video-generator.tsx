"use client";

import React, { useState, useRef, useCallback } from "react";
import Image from "next/image";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";

import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Wand2,
  Upload,
  Image as ImageIcon,
  Video,
  X,
  Sparkles,
  Plus,
} from "lucide-react";
import { useAssetStore } from "@/lib/stores/asset-store";
import { useProjectStore } from "@/lib/stores/project-store";
import { useTimelineStore } from "@/lib/stores/timeline-store";
import { toast } from "sonner";
import type { AISettings } from "@/types/video-editor";

interface AIVideoGeneratorProps {
  className?: string;
  onVideoGenerated?: (assetId: string) => void;
}

interface AITemplate {
  id: string;
  name: string;
  description: string;
  prompt: string;
  settings: Partial<AISettings>;
  category:
    | "cinematic"
    | "animation"
    | "nature"
    | "abstract"
    | "commercial"
    | "social";
  thumbnail?: string;
}

interface GenerationRequest {
  id: string;
  type: "text-to-video" | "image-to-video";
  prompt: string;
  settings: AISettings;
  status: "pending" | "generating" | "completed" | "failed";
  progress: number;
  startTime: Date;
  imageFile?: File;
  error?: string;
  assetId?: string;
}

import { AI_VIDEO_PRESETS } from "@/lib/ai-video-presets";

// Use the first 5 presets as quick templates
const AI_TEMPLATES = AI_VIDEO_PRESETS.slice(0, 5).map((preset) => ({
  id: preset.id,
  name: preset.name,
  description: preset.description,
  prompt: preset.prompt,
  settings: preset.settings,
  category: preset.category,
  thumbnail: preset.thumbnail,
}));

export function AIVideoGenerator({
  className = "",
  onVideoGenerated,
}: AIVideoGeneratorProps) {
  const assetStore = useAssetStore();
  const projectStore = useProjectStore();
  const timelineStore = useTimelineStore();

  // State management
  const [activeTab, setActiveTab] = useState<
    "text-to-video" | "image-to-video"
  >("text-to-video");
  const [textPrompt, setTextPrompt] = useState("");
  const [negativePrompt, setNegativePrompt] = useState("");
  const [selectedTemplate, setSelectedTemplate] = useState<AITemplate | null>(
    null
  );
  const [customSettings, setCustomSettings] = useState<AISettings>({
    model: "fal-ai/ltx-video",
    prompt: "",
    duration: 8,
    aspectRatio: "16:9",
  });

  // Image-to-video state
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [imagePrompt, setImagePrompt] = useState(
    "Animate this image with smooth, natural motion"
  );

  // Generation tracking
  const [generationRequests, setGenerationRequests] = useState<
    GenerationRequest[]
  >([]);

  const fileInputRef = useRef<HTMLInputElement>(null);

  // Add asset to timeline function
  const addAssetToTimeline = useCallback(
    (assetId: string) => {
      const asset = assetStore.assets.find((a) => a.id === assetId);
      if (!asset) {
        toast.error("Asset not found");
        return;
      }

      // Find appropriate track or create new one
      let targetTrack = timelineStore.tracks.find(
        (track) =>
          (asset.type === "video" && track.type === "video") ||
          (asset.type === "audio" && track.type === "audio") ||
          (asset.type === "ai-generated" && track.type === "video") ||
          (asset.type === "image" && track.type === "video")
      );

      if (!targetTrack) {
        // Create new track
        let trackType: "video" | "audio" = "video";
        if (asset.type === "audio") {
          trackType = "audio";
        }

        const existingTracks = timelineStore.tracks.filter(
          (t) => t.type === trackType
        ).length;

        timelineStore.addTrack({
          type: trackType,
          name: `${
            trackType.charAt(0).toUpperCase() + trackType.slice(1)
          } Track ${existingTracks + 1}`,
          clips: [],
          muted: false,
          locked: false,
          height: trackType === "audio" ? 80 : 100,
        });

        targetTrack = timelineStore.tracks.find(
          (track) => track.type === trackType
        );
      }

      if (targetTrack) {
        const newClip = {
          assetId: asset.id,
          startTime: timelineStore.duration,
          endTime: timelineStore.duration + (asset.duration || 5),
          trimStart: 0,
          trimEnd: asset.duration || 5,
          effects: [],
          properties: {
            volume: 1,
            opacity: 1,
            position: { x: 0, y: 0 },
            scale: 1,
            rotation: 0,
            colorGrading: {
              brightness: 100,
              contrast: 100,
              saturation: 100,
            },
          },
        };

        timelineStore.addClip(targetTrack.id, newClip);
        timelineStore.setDuration(
          Math.max(timelineStore.duration, newClip.endTime)
        );

        toast.success("Video added to timeline!");
      }
    },
    [assetStore.assets, timelineStore]
  );

  // Handle image file selection
  const handleImageSelect = useCallback((file: File) => {
    if (!file.type.startsWith("image/")) {
      toast.error("Please select a valid image file");
      return;
    }

    if (file.size > 10 * 1024 * 1024) {
      // 10MB limit
      toast.error("Image file size must be less than 10MB");
      return;
    }

    setSelectedImage(file);

    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setImagePreview(e.target?.result as string);
    };
    reader.readAsDataURL(file);
  }, []);

  // Handle drag and drop for images
  const handleImageDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      const files = Array.from(e.dataTransfer.files);
      if (files.length > 0) {
        handleImageSelect(files[0]);
      }
    },
    [handleImageSelect]
  );

  // Apply template
  const applyTemplate = useCallback((template: AITemplate) => {
    setSelectedTemplate(template);
    setTextPrompt(template.prompt);
    setCustomSettings((prev) => ({
      ...prev,
      ...template.settings,
      prompt: template.prompt,
    }));
  }, []);

  // Generate text-to-video
  const generateTextToVideo = useCallback(async () => {
    if (!textPrompt.trim()) {
      toast.error("Please enter a text prompt");
      return;
    }

    const settings: AISettings = {
      ...customSettings,
      prompt: textPrompt,
      negativePrompt: negativePrompt || undefined,
    };

    try {
      // Set loading state
      assetStore.setGenerationProgress(0);
      assetStore.setGenerating(true);

      // Call the API directly instead of using the asset store
      const response = await fetch("/api/video-editor/assets/generate", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          prompt: textPrompt,
          settings,
          projectId: projectStore.currentProject?.id,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to start generation");
      }

      const result = await response.json();
      const requestId = result.id;

      const request: GenerationRequest = {
        id: requestId,
        type: "text-to-video",
        prompt: textPrompt,
        settings,
        status: "generating",
        progress: 0,
        startTime: new Date(),
      };

      setGenerationRequests((prev) => [...prev, request]);

      // Start polling for progress
      const pollProgress = async (): Promise<void> => {
        try {
          const progressResponse = await fetch(
            `/api/video-editor/assets/generate/${requestId}`
          );

          if (!progressResponse.ok) {
            throw new Error("Failed to check progress");
          }

          const progressData = await progressResponse.json();

          // Update progress in asset store
          assetStore.setGenerationProgress(progressData.progress || 0);

          setGenerationRequests((prev) =>
            prev.map((req) =>
              req.id === requestId
                ? {
                    ...req,
                    status: progressData.status as
                      | "pending"
                      | "generating"
                      | "completed"
                      | "failed",
                    progress: progressData.progress || 0,
                  }
                : req
            )
          );

          if (progressData.status === "completed") {
            // Reset loading state
            assetStore.setGenerating(false);
            assetStore.setGenerationProgress(100);

            // Update the generation request with the asset ID
            setGenerationRequests((prev) =>
              prev.map((req) =>
                req.id === requestId
                  ? {
                      ...req,
                      status: "completed" as const,
                      progress: 100,
                      assetId: progressData.asset.id,
                    }
                  : req
              )
            );

            toast.success("AI video generated successfully!");
            onVideoGenerated?.(progressData.asset.id);

            // Refresh assets to show the new video
            assetStore.loadAssets(projectStore.currentProject?.id);

            // Clear form
            setTextPrompt("");
            setNegativePrompt("");
            setSelectedTemplate(null);
            return;
          } else if (progressData.status === "failed") {
            // Reset loading state
            assetStore.setGenerating(false);
            assetStore.setGenerationProgress(0);

            setGenerationRequests((prev) =>
              prev.map((req) =>
                req.id === requestId
                  ? {
                      ...req,
                      status: "failed" as const,
                      error: progressData.errorMessage || "Generation failed",
                    }
                  : req
              )
            );
            toast.error("AI video generation failed");
            return;
          }

          // Continue polling if still in progress
          if (
            progressData.status === "generating" ||
            progressData.status === "pending"
          ) {
            setTimeout(pollProgress, 3000); // Poll every 3 seconds
          }
        } catch (error) {
          console.error("Error polling progress:", error);
          // Reset loading state
          assetStore.setGenerating(false);
          assetStore.setGenerationProgress(0);

          setGenerationRequests((prev) =>
            prev.map((req) =>
              req.id === requestId
                ? {
                    ...req,
                    status: "failed" as const,
                    error: "Failed to check generation progress",
                  }
                : req
            )
          );
          toast.error("Failed to check generation progress");
        }
      };

      // Start polling
      setTimeout(pollProgress, 2000); // Initial delay
    } catch (error) {
      console.error("AI generation failed:", error);
      // Reset loading state
      assetStore.setGenerating(false);
      assetStore.setGenerationProgress(0);

      toast.error(
        error instanceof Error ? error.message : "Failed to generate AI video"
      );
    }
  }, [
    textPrompt,
    negativePrompt,
    customSettings,
    assetStore,
    projectStore.currentProject?.id,
    onVideoGenerated,
  ]);

  // Generate image-to-video
  const generateImageToVideo = useCallback(async () => {
    if (!selectedImage) {
      toast.error("Please select an image");
      return;
    }

    if (!imagePrompt.trim()) {
      toast.error("Please enter a prompt for image animation");
      return;
    }

    const settings: AISettings = {
      ...customSettings,
      prompt: imagePrompt,
    };

    try {
      // Set loading state
      assetStore.setGenerationProgress(0);
      assetStore.setGenerating(true);

      // Convert image to base64
      const reader = new FileReader();
      reader.onload = async () => {
        const base64 = (reader.result as string).split(",")[1];

        try {
          // Call API for image-to-video generation
          const response = await fetch(
            "/api/video-editor/assets/generate-image-to-video",
            {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({
                imageBase64: base64,
                mimeType: selectedImage.type,
                prompt: imagePrompt,
                settings,
                projectId: projectStore.currentProject?.id,
              }),
            }
          );

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.message || "Failed to start generation");
          }

          const result = await response.json();
          const requestId = result.id;

          const request: GenerationRequest = {
            id: requestId,
            type: "image-to-video",
            prompt: imagePrompt,
            settings,
            status: "generating",
            progress: 0,
            startTime: new Date(),
            imageFile: selectedImage,
          };

          setGenerationRequests((prev) => [...prev, request]);

          // Start polling for progress
          const pollProgress = async (): Promise<void> => {
            try {
              const progressResponse = await fetch(
                `/api/video-editor/assets/generate/${requestId}`
              );

              if (!progressResponse.ok) {
                throw new Error("Failed to check progress");
              }

              const progressData = await progressResponse.json();

              // Update progress in asset store
              assetStore.setGenerationProgress(progressData.progress || 0);

              setGenerationRequests((prev) =>
                prev.map((req) =>
                  req.id === requestId
                    ? {
                        ...req,
                        status: progressData.status as
                          | "pending"
                          | "generating"
                          | "completed"
                          | "failed",
                        progress: progressData.progress || 0,
                      }
                    : req
                )
              );

              if (progressData.status === "completed") {
                // Reset loading state
                assetStore.setGenerating(false);
                assetStore.setGenerationProgress(100);

                toast.success("AI video generated from image successfully!");
                onVideoGenerated?.(progressData.asset.id);

                // Refresh assets to show the new video
                assetStore.loadAssets(projectStore.currentProject?.id);

                // Clear form
                setSelectedImage(null);
                setImagePreview(null);
                setImagePrompt(
                  "Animate this image with smooth, natural motion"
                );
                return;
              } else if (progressData.status === "failed") {
                // Reset loading state
                assetStore.setGenerating(false);
                assetStore.setGenerationProgress(0);

                setGenerationRequests((prev) =>
                  prev.map((req) =>
                    req.id === requestId
                      ? {
                          ...req,
                          status: "failed" as const,
                          error:
                            progressData.errorMessage || "Generation failed",
                        }
                      : req
                  )
                );
                toast.error("AI video generation failed");
                return;
              }

              // Continue polling if still in progress
              if (
                progressData.status === "generating" ||
                progressData.status === "pending"
              ) {
                setTimeout(pollProgress, 3000); // Poll every 3 seconds
              }
            } catch (error) {
              console.error("Error polling progress:", error);
              // Reset loading state
              assetStore.setGenerating(false);
              assetStore.setGenerationProgress(0);

              setGenerationRequests((prev) =>
                prev.map((req) =>
                  req.id === requestId
                    ? {
                        ...req,
                        status: "failed" as const,
                        error: "Failed to check generation progress",
                      }
                    : req
                )
              );
              toast.error("Failed to check generation progress");
            }
          };

          // Start polling
          setTimeout(pollProgress, 2000); // Initial delay
        } catch (error) {
          console.error("Image-to-video generation failed:", error);
          // Reset loading state
          assetStore.setGenerating(false);
          assetStore.setGenerationProgress(0);

          toast.error(
            error instanceof Error
              ? error.message
              : "Failed to generate video from image"
          );
        }
      };

      reader.readAsDataURL(selectedImage);
    } catch (error) {
      console.error("Image processing failed:", error);
      // Reset loading state
      assetStore.setGenerating(false);
      assetStore.setGenerationProgress(0);

      toast.error("Failed to process image");
    }
  }, [
    selectedImage,
    imagePrompt,
    customSettings,
    assetStore,
    projectStore.currentProject?.id,
    onVideoGenerated,
  ]);

  // Cancel generation
  const cancelGeneration = useCallback((requestId: string) => {
    setGenerationRequests((prev) =>
      prev.map((req) =>
        req.id === requestId
          ? { ...req, status: "failed" as const, error: "Cancelled by user" }
          : req
      )
    );
    toast.info("Generation cancelled");
  }, []);

  // Remove completed/failed requests
  const removeRequest = useCallback((requestId: string) => {
    setGenerationRequests((prev) => prev.filter((req) => req.id !== requestId));
  }, []);

  return (
    <div className={`bg-gray-900 text-white ${className}`}>
      <Card className="bg-gray-800 border-gray-700">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-2 text-white">
            <Wand2 className="w-5 h-5 text-purple-400" />
            AI Video Generator
          </CardTitle>
        </CardHeader>

        <CardContent className="space-y-6">
          <Tabs
            value={activeTab}
            onValueChange={(value) => setActiveTab(value as typeof activeTab)}
          >
            <TabsList className="grid w-full grid-cols-2 bg-gray-700">
              <TabsTrigger
                value="text-to-video"
                className="data-[state=active]:bg-purple-600"
              >
                <Video className="w-4 h-4 mr-2" />
                Text to Video
              </TabsTrigger>
              <TabsTrigger
                value="image-to-video"
                className="data-[state=active]:bg-purple-600"
              >
                <ImageIcon className="w-4 h-4 mr-2" />
                Image to Video
              </TabsTrigger>
            </TabsList>

            <TabsContent value="text-to-video" className="space-y-4">
              {/* Templates Section */}
              <div className="space-y-3">
                <Label className="text-sm font-medium text-gray-300">
                  Quick Templates
                </Label>
                <div className="grid grid-cols-1 gap-2 max-h-32 overflow-y-auto">
                  {AI_TEMPLATES.map((template) => (
                    <div
                      key={template.id}
                      className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                        selectedTemplate?.id === template.id
                          ? "border-purple-500 bg-purple-500/10"
                          : "border-gray-600 bg-gray-700/50 hover:border-gray-500"
                      }`}
                      onClick={() => applyTemplate(template)}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <h4 className="text-sm font-medium text-white">
                              {template.name}
                            </h4>
                            <Badge variant="outline" className="text-xs">
                              {template.category}
                            </Badge>
                          </div>
                          <p className="text-xs text-gray-400 mt-1">
                            {template.description}
                          </p>
                        </div>
                        <Sparkles className="w-4 h-4 text-purple-400 flex-shrink-0" />
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <Separator className="bg-gray-700" />

              {/* Text Prompt */}
              <div className="space-y-2">
                <Label
                  htmlFor="text-prompt"
                  className="text-sm font-medium text-gray-300"
                >
                  Video Description
                </Label>
                <Textarea
                  id="text-prompt"
                  placeholder="Describe the video you want to generate..."
                  value={textPrompt}
                  onChange={(e) => setTextPrompt(e.target.value)}
                  className="bg-gray-700 border-gray-600 text-white placeholder-gray-400 min-h-[80px]"
                  rows={3}
                />
              </div>

              {/* Negative Prompt */}
              <div className="space-y-2">
                <Label
                  htmlFor="negative-prompt"
                  className="text-sm font-medium text-gray-300"
                >
                  Negative Prompt (Optional)
                </Label>
                <Input
                  id="negative-prompt"
                  placeholder="What to avoid in the video..."
                  value={negativePrompt}
                  onChange={(e) => setNegativePrompt(e.target.value)}
                  className="bg-gray-700 border-gray-600 text-white placeholder-gray-400"
                />
              </div>

              {/* Generate Button */}
              <Button
                onClick={generateTextToVideo}
                disabled={assetStore.isGenerating || !textPrompt.trim()}
                className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 transform hover:scale-105 active:scale-95"
              >
                {assetStore.isGenerating ? (
                  <>
                    <div className="flex items-center space-x-2">
                      <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      <span className="animate-pulse">Generating Video...</span>
                    </div>
                  </>
                ) : (
                  <>
                    <Wand2 className="w-5 h-5 mr-2 animate-pulse" />
                    <span className="font-semibold">Generate Video</span>
                    <Sparkles className="w-4 h-4 ml-2" />
                  </>
                )}
              </Button>
            </TabsContent>

            <TabsContent value="image-to-video" className="space-y-4">
              {/* Image Upload */}
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-300">
                  Source Image
                </Label>

                {!selectedImage ? (
                  <div
                    className="border-2 border-dashed border-gray-600 rounded-lg p-8 text-center cursor-pointer hover:border-gray-500 transition-colors"
                    onDrop={handleImageDrop}
                    onDragOver={(e) => e.preventDefault()}
                    onClick={() => fileInputRef.current?.click()}
                  >
                    <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                    <p className="text-sm text-gray-400">
                      Drop an image here or click to browse
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      Supports JPG, PNG, WebP (max 10MB)
                    </p>
                  </div>
                ) : (
                  <div className="relative">
                    <Image
                      src={imagePreview!}
                      alt="Selected image"
                      width={320}
                      height={128}
                      className="w-full h-32 object-cover rounded-lg"
                    />
                    <Button
                      size="sm"
                      variant="destructive"
                      className="absolute top-2 right-2"
                      onClick={() => {
                        setSelectedImage(null);
                        setImagePreview(null);
                      }}
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>
                )}

                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  className="hidden"
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) handleImageSelect(file);
                  }}
                />
              </div>

              {/* Animation Prompt */}
              <div className="space-y-2">
                <Label
                  htmlFor="image-prompt"
                  className="text-sm font-medium text-gray-300"
                >
                  Animation Description
                </Label>
                <Textarea
                  id="image-prompt"
                  placeholder="Describe how you want the image to be animated..."
                  value={imagePrompt}
                  onChange={(e) => setImagePrompt(e.target.value)}
                  className="bg-gray-700 border-gray-600 text-white placeholder-gray-400"
                  rows={2}
                />
              </div>

              {/* Generate Button */}
              <Button
                onClick={generateImageToVideo}
                disabled={
                  assetStore.isGenerating ||
                  !selectedImage ||
                  !imagePrompt.trim()
                }
                className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 transform hover:scale-105 active:scale-95"
              >
                {assetStore.isGenerating ? (
                  <>
                    <div className="flex items-center space-x-2">
                      <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      <span className="animate-pulse">Animating Image...</span>
                    </div>
                  </>
                ) : (
                  <>
                    <Wand2 className="w-5 h-5 mr-2 animate-pulse" />
                    <span className="font-semibold">Animate Image</span>
                    <Sparkles className="w-4 h-4 ml-2" />
                  </>
                )}
              </Button>
            </TabsContent>
          </Tabs>

          {/* Generation Progress */}
          {assetStore.isGenerating && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label className="text-sm font-medium text-gray-300">
                  Generation Progress
                </Label>
                <span className="text-sm text-gray-400">
                  {assetStore.generationProgress}%
                </span>
              </div>
              <Progress
                value={assetStore.generationProgress}
                className="bg-gray-700"
              />
            </div>
          )}

          {/* Generation History */}
          {generationRequests.length > 0 && (
            <div className="space-y-3">
              <Label className="text-sm font-medium text-gray-300">
                Recent Generations
              </Label>
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {generationRequests
                  .slice(-5)
                  .reverse()
                  .map((request) => (
                    <div
                      key={request.id}
                      className="flex items-center justify-between p-3 bg-gray-800 rounded-lg border border-gray-700"
                    >
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          {request.type === "text-to-video" ? (
                            <Video className="w-4 h-4 text-blue-400" />
                          ) : (
                            <ImageIcon className="w-4 h-4 text-green-400" />
                          )}
                          <span className="text-sm font-medium text-white truncate">
                            {request.prompt.length > 30
                              ? `${request.prompt.substring(0, 30)}...`
                              : request.prompt}
                          </span>
                        </div>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge
                            variant={
                              request.status === "completed"
                                ? "default"
                                : request.status === "failed"
                                ? "destructive"
                                : "secondary"
                            }
                            className="text-xs"
                          >
                            {request.status}
                          </Badge>
                          {request.status === "generating" && (
                            <div className="flex-1">
                              <Progress
                                value={request.progress}
                                className="h-1"
                              />
                            </div>
                          )}
                        </div>
                        {request.error && (
                          <p className="text-xs text-red-400 mt-1">
                            {request.error}
                          </p>
                        )}
                      </div>

                      <div className="flex items-center gap-1 ml-2">
                        {request.status === "generating" && (
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => cancelGeneration(request.id)}
                            className="h-8 w-8 p-0 text-gray-400 hover:text-white"
                          >
                            <X className="w-4 h-4" />
                          </Button>
                        )}
                        {request.status === "completed" && request.assetId && (
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => addAssetToTimeline(request.assetId!)}
                            className="h-8 w-8 p-0 text-green-400 hover:text-green-300"
                            title="Add to Timeline"
                          >
                            <Plus className="w-4 h-4" />
                          </Button>
                        )}
                        {(request.status === "completed" ||
                          request.status === "failed") && (
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => removeRequest(request.id)}
                            className="h-8 w-8 p-0 text-gray-400 hover:text-white"
                          >
                            <X className="w-4 h-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                  ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
