"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { X, Menu } from "lucide-react";
import { cn } from "@/lib/utils";

interface ResponsiveSidebarProps {
  children: React.ReactNode;
  className?: string;
  side?: "left" | "right";
  title?: string;
  defaultOpen?: boolean;
}

export function ResponsiveSidebar({
  children,
  className,
  side = "left",
  title,
  defaultOpen = true,
}: ResponsiveSidebarProps) {
  const [isOpen, setIsOpen] = useState(defaultOpen);

  return (
    <>
      {/* Mobile Toggle Button */}
      <Button
        variant="ghost"
        size="sm"
        className={cn(
          "lg:hidden fixed top-20 z-50 bg-gray-800 hover:bg-gray-700 text-white",
          side === "left" ? "left-4" : "right-4"
        )}
        onClick={() => setIsOpen(!isOpen)}
      >
        <Menu className="w-4 h-4" />
        {title && <span className="ml-2 text-xs">{title}</span>}
      </Button>

      {/* Mobile Overlay */}
      {isOpen && (
        <div
          className="lg:hidden fixed inset-0 bg-black/50 z-40"
          onClick={() => setIsOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div
        className={cn(
          "bg-gray-900 border-gray-700 overflow-y-auto transition-transform duration-300 ease-in-out z-50",
          side === "left" ? "border-r" : "border-l",
          // Desktop styles
          "lg:relative lg:translate-x-0 lg:w-80 lg:flex-shrink-0",
          // Mobile styles
          "fixed top-0 w-80 h-full",
          side === "left" ? "left-0" : "right-0",
          isOpen ? "translate-x-0" : side === "left" ? "-translate-x-full" : "translate-x-full",
          className
        )}
      >
        {/* Mobile Header */}
        <div className="lg:hidden flex items-center justify-between p-4 border-b border-gray-700">
          {title && <h3 className="text-white font-medium">{title}</h3>}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsOpen(false)}
            className="text-gray-300"
          >
            <X className="w-4 h-4" />
          </Button>
        </div>

        {/* Content */}
        <div className="h-full lg:h-auto">{children}</div>
      </div>
    </>
  );
}
