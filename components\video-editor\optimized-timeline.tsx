"use client";

import React, { useMemo, useCallback, useRef, useEffect } from "react";
import Image from "next/image";
import { VirtualTimeline } from "@/components/ui/virtual-scroll";
import { useTimelineStore } from "@/lib/stores/timeline-store";
import { useAnalyticsManager } from "@/lib/services/analytics-service";
import { useMemoryManager } from "@/lib/services/memory-management-service";
import { useCacheManager } from "@/lib/services/cache-service";
import { cn } from "@/lib/utils";
import type { Track, Clip } from "@/types/video-editor";

interface OptimizedTimelineProps {
  className?: string;
  containerHeight?: number;
}

export function OptimizedTimeline({
  className,
  containerHeight = 400,
}: OptimizedTimelineProps) {
  const tracks = useTimelineStore((state) => state.tracks);
  const zoomLevel = useTimelineStore((state) => state.zoomLevel);
  const duration = useTimelineStore((state) => state.duration);
  const playheadPosition = useTimelineStore((state) => state.playheadPosition);
  const selectedClips = useTimelineStore((state) => state.selectedClips);
  const setPlayheadPosition = useTimelineStore(
    (state) => state.setPlayheadPosition
  );
  const selectClip = useTimelineStore((state) => state.selectClip);

  const { trackTimelineInteraction } = useAnalyticsManager();
  const { getMemoryUsage } = useMemoryManager();
  const { previewFrames } = useCacheManager();

  const timelineRef = useRef<HTMLDivElement>(null);
  const lastInteractionRef = useRef<number>(0);

  // Optimized time to pixels conversion
  const timeToPixels = useCallback(
    (time: number): number => {
      return time * zoomLevel * 50; // 50 pixels per second at 1x zoom
    },
    [zoomLevel]
  );

  // Memoized timeline data for performance
  const timelineData = useMemo(() => {
    return tracks.map((track) => ({
      ...track,
      height: track.height || 100,
      optimizedClips: track.clips.map((clip) => ({
        ...clip,
        // Pre-calculate positions for better performance
        pixelStart: timeToPixels(clip.startTime),
        pixelEnd: timeToPixels(clip.endTime),
        pixelWidth: timeToPixels(clip.endTime - clip.startTime),
      })),
    }));
  }, [tracks, timeToPixels]);

  // Optimized pixels to time conversion
  const pixelsToTime = useCallback(
    (pixels: number): number => {
      return pixels / (zoomLevel * 50);
    },
    [zoomLevel]
  );

  // Throttled interaction tracking
  const trackInteraction = useCallback(
    (action: string) => {
      const now = Date.now();
      if (now - lastInteractionRef.current > 1000) {
        // Throttle to once per second
        trackTimelineInteraction(action, tracks.length, duration);
        lastInteractionRef.current = now;
      }
    },
    [trackTimelineInteraction, tracks.length, duration]
  );

  // Optimized clip rendering with memoization
  const renderClip = useCallback(
    (
      clip: Clip & { pixelStart: number; pixelEnd: number; pixelWidth: number }
    ) => {
      const isSelected = selectedClips.includes(clip.id);

      return (
        <div
          key={clip.id}
          className={cn(
            "absolute top-2 bottom-2 rounded cursor-move transition-colors",
            isSelected
              ? "bg-purple-600 border-2 border-purple-400"
              : "bg-blue-600 hover:bg-blue-500 border border-blue-400"
          )}
          style={{
            left: clip.pixelStart,
            width: Math.max(clip.pixelWidth, 20),
          }}
          onClick={(e) => {
            e.stopPropagation();
            selectClip(clip.id, e.ctrlKey || e.metaKey);
            trackInteraction("clip_select");
          }}
          onDoubleClick={() => {
            trackInteraction("clip_edit");
          }}
        >
          <div className="p-2 text-xs text-white truncate">
            Clip {clip.id.slice(0, 8)}
          </div>

          {/* Clip thumbnail preview */}
          <ClipThumbnail clip={clip} />

          {/* Resize handles */}
          <div className="absolute left-0 top-0 bottom-0 w-1 bg-white opacity-0 hover:opacity-100 cursor-ew-resize" />
          <div className="absolute right-0 top-0 bottom-0 w-1 bg-white opacity-0 hover:opacity-100 cursor-ew-resize" />
        </div>
      );
    },
    [trackInteraction, selectedClips, selectClip]
  );

  // Optimized track rendering
  const renderTrack = useCallback(
    (trackData: unknown) => {
      const track = trackData as Track & {
        optimizedClips: (Clip & {
          pixelStart: number;
          pixelEnd: number;
          pixelWidth: number;
        })[];
      };

      return (
        <div
          key={track.id}
          className="relative bg-gray-800 border-b border-gray-700"
          style={{ height: track.height }}
        >
          {/* Track header */}
          <div className="absolute left-0 top-0 w-32 h-full bg-gray-800 border-r border-gray-700 flex items-center px-3 z-10">
            <div>
              <div className="text-sm font-medium text-white truncate">
                {track.name}
              </div>
              <div className="text-xs text-gray-400 capitalize">
                {track.type}
              </div>
            </div>
          </div>

          {/* Track content area */}
          <div className="ml-32 relative h-full">
            {track.optimizedClips.map(renderClip)}
          </div>
        </div>
      );
    },
    [renderClip]
  );

  // Memory usage monitoring
  useEffect(() => {
    const interval = setInterval(() => {
      const usage = getMemoryUsage();
      if (usage.percentage > 80) {
        console.warn("High memory usage in timeline:", usage);
        // Clear low priority cache entries
        previewFrames.clearVideo("low-priority");
      }
    }, 10000);

    return () => clearInterval(interval);
  }, [getMemoryUsage, previewFrames]);

  // Performance monitoring
  useEffect(() => {
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.name.includes("timeline")) {
          console.log(
            `Timeline performance: ${entry.name} took ${entry.duration}ms`
          );
        }
      }
    });

    observer.observe({ entryTypes: ["measure"] });

    return () => observer.disconnect();
  }, []);

  return (
    <div className={cn("flex flex-col h-full bg-gray-900", className)}>
      {/* Timeline ruler */}
      <TimelineRuler
        duration={duration}
        zoomLevel={zoomLevel}
        timeToPixels={timeToPixels}
      />

      {/* Virtual scrolling timeline */}
      <div className="flex-1 relative" ref={timelineRef}>
        <VirtualTimeline
          tracks={timelineData}
          containerHeight={containerHeight}
          renderTrack={renderTrack}
          className="h-full"
        />

        {/* Playhead */}
        <Playhead
          position={timeToPixels(playheadPosition)}
          height={containerHeight}
          onPositionChange={(pixels) => {
            const time = pixelsToTime(pixels);
            setPlayheadPosition(time);
            trackInteraction("playhead_move");
          }}
        />
      </div>
    </div>
  );
}

// Optimized clip thumbnail component
interface ClipThumbnailProps {
  clip: Clip;
}

function ClipThumbnail({ clip }: ClipThumbnailProps) {
  const { previewFrames } = useCacheManager();
  const [thumbnail, setThumbnail] = React.useState<string | null>(null);

  useEffect(() => {
    // Check cache first
    const cachedFrame = previewFrames.getFrame(clip.assetId, 0);
    if (cachedFrame) {
      if (cachedFrame instanceof HTMLCanvasElement) {
        setThumbnail(cachedFrame.toDataURL());
      }
      return;
    }

    // Load thumbnail asynchronously
    const loadThumbnail = async () => {
      try {
        // This would integrate with the progressive loading service
        // For now, we'll use a placeholder
        setThumbnail("/api/placeholder/thumbnail");
      } catch (error) {
        console.error("Failed to load thumbnail:", error);
      }
    };

    loadThumbnail();
  }, [clip.assetId, previewFrames]);

  if (!thumbnail) {
    return (
      <div className="absolute inset-0 bg-gray-700 flex items-center justify-center">
        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
      </div>
    );
  }

  return (
    <Image
      src={thumbnail}
      alt="Clip thumbnail"
      fill
      className="object-cover opacity-30"
      loading="lazy"
    />
  );
}

// Timeline ruler component
interface TimelineRulerProps {
  duration: number;
  zoomLevel: number;
  timeToPixels: (time: number) => number;
}

function TimelineRuler({
  duration,
  zoomLevel,
  timeToPixels,
}: TimelineRulerProps) {
  const ticks = useMemo(() => {
    const tickInterval = Math.max(1, Math.floor(10 / zoomLevel)); // Adjust tick density based on zoom
    const ticks = [];

    for (let time = 0; time <= duration; time += tickInterval) {
      ticks.push({
        time,
        position: timeToPixels(time),
        label: formatTime(time),
      });
    }

    return ticks;
  }, [duration, zoomLevel, timeToPixels]);

  return (
    <div className="h-8 bg-gray-800 border-b border-gray-700 relative overflow-hidden">
      <div className="ml-32 relative h-full">
        {ticks.map((tick) => (
          <div
            key={tick.time}
            className="absolute top-0 bottom-0 border-l border-gray-600"
            style={{ left: tick.position }}
          >
            <div className="text-xs text-gray-400 mt-1 ml-1">{tick.label}</div>
          </div>
        ))}
      </div>
    </div>
  );
}

// Playhead component
interface PlayheadProps {
  position: number;
  height: number;
  onPositionChange: (position: number) => void;
}

function Playhead({ position, height, onPositionChange }: PlayheadProps) {
  const [isDragging, setIsDragging] = React.useState(false);

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    setIsDragging(true);
    e.preventDefault();
  }, []);

  useEffect(() => {
    if (!isDragging) return;

    const handleMouseMove = (e: MouseEvent) => {
      const rect = document
        .querySelector(".timeline-container")
        ?.getBoundingClientRect();
      if (rect) {
        const x = e.clientX - rect.left - 128; // Account for track header width
        onPositionChange(Math.max(0, x));
      }
    };

    const handleMouseUp = () => {
      setIsDragging(false);
    };

    document.addEventListener("mousemove", handleMouseMove);
    document.addEventListener("mouseup", handleMouseUp);

    return () => {
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleMouseUp);
    };
  }, [isDragging, onPositionChange]);

  return (
    <div
      className="absolute top-0 w-0.5 bg-red-500 cursor-ew-resize z-20 timeline-container"
      style={{
        left: position + 128, // Account for track header width
        height,
      }}
      onMouseDown={handleMouseDown}
    >
      <div className="absolute -top-2 -left-2 w-4 h-4 bg-red-500 rounded-full" />
    </div>
  );
}

// Utility functions
function formatTime(seconds: number): string {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
}
