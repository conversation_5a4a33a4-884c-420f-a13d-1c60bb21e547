export interface VideoMetadata {
  duration: number;
  width: number;
  height: number;
  fps: number;
  hasAudio: boolean;
  fileSize: number;
  mimeType: string;
}

export async function getVideoMetadata(file: File): Promise<VideoMetadata> {
  return new Promise((resolve, reject) => {
    const video = document.createElement('video');
    const url = URL.createObjectURL(file);
    
    video.onloadedmetadata = () => {
      const metadata: VideoMetadata = {
        duration: video.duration,
        width: video.videoWidth,
        height: video.videoHeight,
        fps: 30, // Default fallback
        hasAudio: false,
        fileSize: file.size,
        mimeType: file.type,
      };

      // Try to detect frame rate using video capture
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      if (ctx) {
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        
        let frameCount = 0;
        let lastTime = 0;
        const frameTimes: number[] = [];
        
        const detectFrameRate = () => {
          if (frameCount < 30 && video.currentTime < Math.min(1, video.duration)) {
            const currentTime = video.currentTime;
            if (lastTime > 0) {
              const deltaTime = currentTime - lastTime;
              if (deltaTime > 0) {
                frameTimes.push(deltaTime);
              }
            }
            lastTime = currentTime;
            frameCount++;
            
            video.currentTime += 0.033; // Advance by ~1 frame at 30fps
            requestAnimationFrame(detectFrameRate);
          } else {
            // Calculate average frame rate
            if (frameTimes.length > 0) {
              const avgFrameTime = frameTimes.reduce((a, b) => a + b, 0) / frameTimes.length;
              const detectedFps = 1 / avgFrameTime;
              
              // Round to common frame rates
              if (detectedFps > 55 && detectedFps < 65) {
                metadata.fps = 60;
              } else if (detectedFps > 45 && detectedFps < 55) {
                metadata.fps = 50;
              } else if (detectedFps > 28 && detectedFps < 32) {
                metadata.fps = 30;
              } else if (detectedFps > 23 && detectedFps < 26) {
                metadata.fps = 24;
              } else {
                metadata.fps = Math.round(detectedFps);
              }
            }
            
            // Check for audio tracks
            if ('webkitAudioDecodedByteCount' in video && video.webkitAudioDecodedByteCount > 0) {
              metadata.hasAudio = true;
            } else if ('mozHasAudio' in video && video.mozHasAudio) {
              metadata.hasAudio = true;
            } else {
              // Fallback: check if the video element can play audio
              metadata.hasAudio = video.duration > 0 && !video.muted;
            }
            
            URL.revokeObjectURL(url);
            resolve(metadata);
          }
        };
        
        video.currentTime = 0;
        detectFrameRate();
      } else {
        // Fallback without frame rate detection
        URL.revokeObjectURL(url);
        resolve(metadata);
      }
    };
    
    video.onerror = () => {
      URL.revokeObjectURL(url);
      reject(new Error('Failed to load video metadata'));
    };
    
    video.src = url;
    video.load();
  });
}

export async function getAudioMetadata(file: File): Promise<Partial<VideoMetadata>> {
  return new Promise((resolve, reject) => {
    const audio = document.createElement('audio');
    const url = URL.createObjectURL(file);
    
    audio.onloadedmetadata = () => {
      const metadata: Partial<VideoMetadata> = {
        duration: audio.duration,
        hasAudio: true,
        fileSize: file.size,
        mimeType: file.type,
      };
      
      URL.revokeObjectURL(url);
      resolve(metadata);
    };
    
    audio.onerror = () => {
      URL.revokeObjectURL(url);
      reject(new Error('Failed to load audio metadata'));
    };
    
    audio.src = url;
    audio.load();
  });
}

export function getOptimalProjectFPS(assets: { fps?: number; type: string }[]): number {
  const videoAssets = assets.filter(asset => 
    asset.type === 'video' || asset.type === 'ai-generated'
  );
  
  if (videoAssets.length === 0) {
    return 30; // Default
  }
  
  // Find the highest FPS among video assets
  const maxFps = Math.max(...videoAssets.map(asset => asset.fps || 30));
  
  // Round to common frame rates
  if (maxFps >= 60) return 60;
  if (maxFps >= 50) return 50;
  if (maxFps >= 30) return 30;
  if (maxFps >= 24) return 24;
  
  return 30; // Fallback
}

export function suggestProjectSettings(assets: { 
  width?: number; 
  height?: number; 
  fps?: number; 
  type: string 
}[]): {
  width: number;
  height: number;
  fps: number;
} {
  const videoAssets = assets.filter(asset => 
    asset.type === 'video' || asset.type === 'ai-generated'
  );
  
  if (videoAssets.length === 0) {
    return { width: 1920, height: 1080, fps: 30 };
  }
  
  // Find the most common resolution
  const resolutions = videoAssets.map(asset => ({
    width: asset.width || 1920,
    height: asset.height || 1080,
  }));
  
  // Use the first video's resolution as base, or highest resolution
  const maxResolution = resolutions.reduce((max, current) => {
    const maxPixels = max.width * max.height;
    const currentPixels = current.width * current.height;
    return currentPixels > maxPixels ? current : max;
  }, resolutions[0]);
  
  return {
    width: maxResolution.width,
    height: maxResolution.height,
    fps: getOptimalProjectFPS(assets),
  };
}
