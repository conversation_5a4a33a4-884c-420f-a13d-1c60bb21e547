import React from "react";
import {
  AbsoluteFill,
  interpolate,
  useCurrentFrame,
  useVideoConfig,
} from "remotion";
import type { Effect } from "@/types/video-editor";

// Remotion effect components that can be applied to video clips

interface EffectWrapperProps {
  children: React.ReactNode;
  effects: Effect[];
}

export function EffectWrapper({ children, effects }: EffectWrapperProps) {
  const frame = useCurrentFrame();
  const { fps, durationInFrames } = useVideoConfig();

  // Apply all effects in sequence
  let wrappedContent = children;

  effects.forEach((effect) => {
    if (!effect.enabled) return;

    switch (effect.type) {
      case "blur":
        wrappedContent = (
          <BlurEffect intensity={effect.parameters.intensity as number}>
            {wrappedContent}
          </BlurEffect>
        );
        break;
      case "sepia":
        wrappedContent = (
          <SepiaEffect intensity={effect.parameters.intensity as number}>
            {wrappedContent}
          </SepiaEffect>
        );
        break;
      case "grayscale":
        wrappedContent = (
          <GrayscaleEffect intensity={effect.parameters.intensity as number}>
            {wrappedContent}
          </GrayscaleEffect>
        );
        break;
      case "invert":
        wrappedContent = (
          <InvertEffect intensity={effect.parameters.intensity as number}>
            {wrappedContent}
          </InvertEffect>
        );
        break;
      case "fade":
        wrappedContent = (
          <FadeEffect
            type={effect.parameters.type as "in" | "out"}
            duration={effect.parameters.duration as number}
            fps={fps}
            durationInFrames={durationInFrames}
            currentFrame={frame}
          >
            {wrappedContent}
          </FadeEffect>
        );
        break;
      case "slide":
        wrappedContent = (
          <SlideEffect
            direction={
              effect.parameters.direction as "left" | "right" | "up" | "down"
            }
            duration={effect.parameters.duration as number}
            fps={fps}
            durationInFrames={durationInFrames}
            currentFrame={frame}
          >
            {wrappedContent}
          </SlideEffect>
        );
        break;
      case "zoom":
        wrappedContent = (
          <ZoomEffect
            type={effect.parameters.type as "in" | "out"}
            duration={effect.parameters.duration as number}
            fps={fps}
            durationInFrames={durationInFrames}
            currentFrame={frame}
          >
            {wrappedContent}
          </ZoomEffect>
        );
        break;
      case "dissolve":
        wrappedContent = (
          <DissolveEffect
            duration={effect.parameters.duration as number}
            fps={fps}
            durationInFrames={durationInFrames}
            currentFrame={frame}
          >
            {wrappedContent}
          </DissolveEffect>
        );
        break;
    }
  });

  return <>{wrappedContent}</>;
}

// Individual effect components

interface BlurEffectProps {
  children: React.ReactNode;
  intensity: number;
}

function BlurEffect({ children, intensity }: BlurEffectProps) {
  return (
    <div
      style={{
        filter: `blur(${intensity}px)`,
        width: "100%",
        height: "100%",
      }}
    >
      {children}
    </div>
  );
}

interface SepiaEffectProps {
  children: React.ReactNode;
  intensity: number;
}

function SepiaEffect({ children, intensity }: SepiaEffectProps) {
  return (
    <div
      style={{
        filter: `sepia(${intensity}%)`,
        width: "100%",
        height: "100%",
      }}
    >
      {children}
    </div>
  );
}

interface GrayscaleEffectProps {
  children: React.ReactNode;
  intensity: number;
}

function GrayscaleEffect({ children, intensity }: GrayscaleEffectProps) {
  return (
    <div
      style={{
        filter: `grayscale(${intensity}%)`,
        width: "100%",
        height: "100%",
      }}
    >
      {children}
    </div>
  );
}

interface InvertEffectProps {
  children: React.ReactNode;
  intensity: number;
}

function InvertEffect({ children, intensity }: InvertEffectProps) {
  return (
    <div
      style={{
        filter: `invert(${intensity}%)`,
        width: "100%",
        height: "100%",
      }}
    >
      {children}
    </div>
  );
}

interface FadeEffectProps {
  children: React.ReactNode;
  type: "in" | "out";
  duration: number;
  fps: number;
  durationInFrames: number;
  currentFrame: number;
}

function FadeEffect({
  children,
  type,
  duration,
  fps,
  durationInFrames,
  currentFrame,
}: FadeEffectProps) {
  const effectDurationInFrames = duration * fps;

  let opacity = 1;

  if (type === "in") {
    // Fade in at the beginning
    if (currentFrame < effectDurationInFrames) {
      opacity = interpolate(currentFrame, [0, effectDurationInFrames], [0, 1], {
        extrapolateLeft: "clamp",
        extrapolateRight: "clamp",
      });
    }
  } else {
    // Fade out at the end
    const fadeStartFrame = durationInFrames - effectDurationInFrames;
    if (currentFrame > fadeStartFrame) {
      opacity = interpolate(
        currentFrame,
        [fadeStartFrame, durationInFrames],
        [1, 0],
        {
          extrapolateLeft: "clamp",
          extrapolateRight: "clamp",
        }
      );
    }
  }

  return (
    <div
      style={{
        opacity,
        width: "100%",
        height: "100%",
      }}
    >
      {children}
    </div>
  );
}

interface SlideEffectProps {
  children: React.ReactNode;
  direction: "left" | "right" | "up" | "down";
  duration: number;
  fps: number;
  durationInFrames: number;
  currentFrame: number;
}

function SlideEffect({
  children,
  direction,
  duration,
  fps,
  currentFrame,
}: SlideEffectProps) {
  const effectDurationInFrames = duration * fps;

  let transform = "translate(0, 0)";

  if (currentFrame < effectDurationInFrames) {
    const progress = interpolate(
      currentFrame,
      [0, effectDurationInFrames],
      [0, 1],
      {
        extrapolateLeft: "clamp",
        extrapolateRight: "clamp",
      }
    );

    switch (direction) {
      case "left":
        transform = `translateX(${interpolate(progress, [0, 1], [-100, 0])}%)`;
        break;
      case "right":
        transform = `translateX(${interpolate(progress, [0, 1], [100, 0])}%)`;
        break;
      case "up":
        transform = `translateY(${interpolate(progress, [0, 1], [-100, 0])}%)`;
        break;
      case "down":
        transform = `translateY(${interpolate(progress, [0, 1], [100, 0])}%)`;
        break;
    }
  }

  return (
    <div
      style={{
        transform,
        width: "100%",
        height: "100%",
      }}
    >
      {children}
    </div>
  );
}

interface ZoomEffectProps {
  children: React.ReactNode;
  type: "in" | "out";
  duration: number;
  fps: number;
  durationInFrames: number;
  currentFrame: number;
}

function ZoomEffect({
  children,
  type,
  duration,
  fps,
  durationInFrames,
  currentFrame,
}: ZoomEffectProps) {
  const effectDurationInFrames = duration * fps;

  let scale = 1;

  if (type === "in") {
    // Zoom in at the beginning
    if (currentFrame < effectDurationInFrames) {
      scale = interpolate(currentFrame, [0, effectDurationInFrames], [0.5, 1], {
        extrapolateLeft: "clamp",
        extrapolateRight: "clamp",
      });
    }
  } else {
    // Zoom out at the end
    const zoomStartFrame = durationInFrames - effectDurationInFrames;
    if (currentFrame > zoomStartFrame) {
      scale = interpolate(
        currentFrame,
        [zoomStartFrame, durationInFrames],
        [1, 1.5],
        {
          extrapolateLeft: "clamp",
          extrapolateRight: "clamp",
        }
      );
    }
  }

  return (
    <div
      style={{
        transform: `scale(${scale})`,
        width: "100%",
        height: "100%",
      }}
    >
      {children}
    </div>
  );
}

interface DissolveEffectProps {
  children: React.ReactNode;
  duration: number;
  fps: number;
  durationInFrames: number;
  currentFrame: number;
}

function DissolveEffect({
  children,
  duration,
  fps,
  durationInFrames,
  currentFrame,
}: DissolveEffectProps) {
  const effectDurationInFrames = duration * fps;
  const dissolveStartFrame = durationInFrames - effectDurationInFrames;

  let opacity = 1;

  if (currentFrame > dissolveStartFrame) {
    opacity = interpolate(
      currentFrame,
      [dissolveStartFrame, durationInFrames],
      [1, 0],
      {
        extrapolateLeft: "clamp",
        extrapolateRight: "clamp",
      }
    );
  }

  return (
    <div
      style={{
        opacity,
        width: "100%",
        height: "100%",
        filter: `blur(${interpolate(opacity, [1, 0], [0, 5])}px)`,
      }}
    >
      {children}
    </div>
  );
}

// Color grading component that applies filters based on clip properties
interface ColorGradingProps {
  children: React.ReactNode;
  brightness?: number;
  contrast?: number;
  saturation?: number;
  hue?: number;
}

export function ColorGrading({
  children,
  brightness = 100,
  contrast = 100,
  saturation = 100,
  hue = 0,
}: ColorGradingProps) {
  const filterString = [
    `brightness(${brightness}%)`,
    `contrast(${contrast}%)`,
    `saturate(${saturation}%)`,
    `hue-rotate(${hue}deg)`,
  ].join(" ");

  return (
    <div
      style={{
        filter: filterString,
        width: "100%",
        height: "100%",
      }}
    >
      {children}
    </div>
  );
}

// Transform component that applies scale, rotation, position, and opacity
interface TransformProps {
  children: React.ReactNode;
  scale?: number;
  rotation?: number;
  position?: { x: number; y: number };
  opacity?: number;
}

export function Transform({
  children,
  scale = 1,
  rotation = 0,
  position = { x: 0, y: 0 },
  opacity = 1,
}: TransformProps) {
  const transformString = [
    `scale(${scale})`,
    `rotate(${rotation}deg)`,
    `translate(${position.x}px, ${position.y}px)`,
  ].join(" ");

  return (
    <div
      style={{
        transform: transformString,
        opacity,
        width: "100%",
        height: "100%",
      }}
    >
      {children}
    </div>
  );
}

// Main video clip component that combines all effects and transforms
interface VideoClipProps {
  src: string;
  effects: Effect[];
  properties: {
    scale?: number;
    rotation?: number;
    position?: { x: number; y: number };
    opacity?: number;
    volume?: number;
    filters?: {
      brightness?: number;
      contrast?: number;
      saturation?: number;
      hue?: number;
    };
  };
  muted?: boolean;
}

export function VideoClip({
  src,
  effects,
  properties,
  muted = false,
}: VideoClipProps) {
  return (
    <AbsoluteFill>
      <EffectWrapper effects={effects}>
        <ColorGrading
          brightness={properties.filters?.brightness}
          contrast={properties.filters?.contrast}
          saturation={properties.filters?.saturation}
          hue={properties.filters?.hue}
        >
          <Transform
            scale={properties.scale}
            rotation={properties.rotation}
            position={properties.position}
            opacity={properties.opacity}
          >
            <video
              src={src}
              style={{
                width: "100%",
                height: "100%",
                objectFit: "cover",
              }}
              muted={muted}
              volume={properties.volume || 1}
            />
          </Transform>
        </ColorGrading>
      </EffectWrapper>
    </AbsoluteFill>
  );
}
