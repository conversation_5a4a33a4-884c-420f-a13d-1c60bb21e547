"use client";

import React, { useRef, useEffect, useState, useCallback } from "react";
import { cn } from "@/lib/utils";

interface AudioWaveformProps {
  audioUrl: string;
  width: number;
  height: number;
  className?: string;
  color?: string;
  backgroundColor?: string;
  progress?: number;
  onLoadComplete?: (duration: number) => void;
  onError?: (error: Error) => void;
}

export function AudioWaveform({
  audioUrl,
  width,
  height,
  className,
  color = "#3b82f6",
  backgroundColor = "transparent",
  progress = 0,
  onLoadComplete,
  onError,
}: AudioWaveformProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [waveformData, setWaveformData] = useState<Float32Array | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [duration, setDuration] = useState(0);

  const generateWaveform = useCallback(async (url: string) => {
    try {
      setIsLoading(true);
      
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to fetch audio: ${response.statusText}`);
      }

      const arrayBuffer = await response.arrayBuffer();
      const audioContext = new AudioContext();
      const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);

      const channelData = audioBuffer.getChannelData(0);
      const samples = Math.min(width * 2, 2000);
      const blockSize = Math.floor(channelData.length / samples);
      const waveform = new Float32Array(samples);

      for (let i = 0; i < samples; i++) {
        let sum = 0;
        const start = i * blockSize;
        const end = Math.min(start + blockSize, channelData.length);
        
        for (let j = start; j < end; j++) {
          sum += Math.abs(channelData[j]);
        }
        waveform[i] = sum / (end - start);
      }

      const maxAmplitude = Math.max(...waveform);
      if (maxAmplitude > 0) {
        for (let i = 0; i < waveform.length; i++) {
          waveform[i] = waveform[i] / maxAmplitude;
        }
      }

      setWaveformData(waveform);
      setDuration(audioBuffer.duration);
      setIsLoading(false);
      
      if (onLoadComplete) {
        onLoadComplete(audioBuffer.duration);
      }

      audioContext.close();
    } catch (error) {
      console.error("Error generating waveform:", error);
      setIsLoading(false);
      if (onError) {
        onError(error instanceof Error ? error : new Error("Unknown error"));
      }
    }
  }, [width, onLoadComplete, onError]);

  useEffect(() => {
    if (audioUrl) {
      generateWaveform(audioUrl);
    }
  }, [audioUrl, generateWaveform]);

  const drawWaveform = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas || !waveformData) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    canvas.width = width;
    canvas.height = height;

    ctx.fillStyle = backgroundColor;
    ctx.fillRect(0, 0, width, height);

    const barWidth = width / waveformData.length;
    const centerY = height / 2;
    const maxBarHeight = height * 0.8;

    ctx.fillStyle = color;

    for (let i = 0; i < waveformData.length; i++) {
      const barHeight = waveformData[i] * maxBarHeight;
      const x = i * barWidth;
      const y = centerY - barHeight / 2;

      ctx.fillRect(x, y, Math.max(1, barWidth - 1), barHeight);
    }

    if (progress > 0 && progress <= 1) {
      const progressX = width * progress;
      
      ctx.fillStyle = "rgba(255, 255, 255, 0.3)";
      ctx.fillRect(0, 0, progressX, height);
      
      ctx.strokeStyle = "#ffffff";
      ctx.lineWidth = 2;
      ctx.beginPath();
      ctx.moveTo(progressX, 0);
      ctx.lineTo(progressX, height);
      ctx.stroke();
    }
  }, [waveformData, width, height, color, backgroundColor, progress]);

  useEffect(() => {
    drawWaveform();
  }, [drawWaveform]);

  if (isLoading) {
    return (
      <div
        className={cn(
          "flex items-center justify-center bg-gray-800 rounded",
          className
        )}
        style={{ width, height }}
      >
        <div className="text-xs text-gray-400">Loading waveform...</div>
      </div>
    );
  }

  if (!waveformData) {
    return (
      <div
        className={cn(
          "flex items-center justify-center bg-gray-800 rounded",
          className
        )}
        style={{ width, height }}
      >
        <div className="text-xs text-red-400">Failed to load audio</div>
      </div>
    );
  }

  return (
    <canvas
      ref={canvasRef}
      className={cn("rounded", className)}
      style={{ width, height }}
    />
  );
}

interface AudioClipWaveformProps {
  audioUrl: string;
  clipWidth: number;
  clipHeight: number;
  startTime: number;
  endTime: number;
  trimStart?: number;
  trimEnd?: number;
  className?: string;
  progress?: number;
}

export function AudioClipWaveform({
  audioUrl,
  clipWidth,
  clipHeight,
  startTime,
  endTime,
  trimStart = 0,
  trimEnd = 0,
  className,
  progress = 0,
}: AudioClipWaveformProps) {
  const clipDuration = endTime - startTime;
  const effectiveStart = trimStart;
  const effectiveEnd = clipDuration - trimEnd;
  const effectiveDuration = effectiveEnd - effectiveStart;
  
  const progressInClip = Math.max(0, Math.min(1, 
    (progress - startTime) / clipDuration
  ));

  return (
    <div className="relative overflow-hidden">
      <AudioWaveform
        audioUrl={audioUrl}
        width={clipWidth}
        height={clipHeight}
        className={className}
        color="#10b981"
        backgroundColor="rgba(16, 185, 129, 0.1)"
        progress={progressInClip}
      />
      
      {trimStart > 0 && (
        <div
          className="absolute top-0 left-0 bg-black bg-opacity-50"
          style={{
            width: `${(trimStart / clipDuration) * 100}%`,
            height: "100%",
          }}
        />
      )}
      
      {trimEnd > 0 && (
        <div
          className="absolute top-0 right-0 bg-black bg-opacity-50"
          style={{
            width: `${(trimEnd / clipDuration) * 100}%`,
            height: "100%",
          }}
        />
      )}
    </div>
  );
}
