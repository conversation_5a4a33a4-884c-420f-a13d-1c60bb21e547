"use client";

import { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { PlayerRef } from "@remotion/player";

import { Save, Keyboard, HelpCircle, Maximize, Minimize } from "lucide-react";
import { useTimelineStore } from "@/lib/stores/timeline-store";
import { useProjectStore } from "@/lib/stores/project-store";
import { useAssetStore } from "@/lib/stores/asset-store";
import { useSidebar } from "@/lib/contexts/sidebar-context";
import { ErrorBoundary } from "@/components/ui/error-boundary";
import { ToastContainer } from "@/components/ui/toast";
import { GlobalLoadingIndicator } from "@/components/ui/loading-indicators";
import { PerformanceDashboard } from "@/components/video-editor/performance-dashboard";
import { OptimizedTimeline } from "@/components/video-editor/optimized-timeline";
import { useAnalyticsManager } from "@/lib/services/analytics-service";
import { useMemoryManager } from "@/lib/services/memory-management-service";
import { setupWebVitalsTracking } from "@/lib/services/analytics-service";
import { ClipManager } from "@/components/video-editor/clip-manager";
import { RemotionPreview } from "@/components/video-editor/remotion-preview";
import { ResponsiveSidebar } from "@/components/video-editor/responsive-sidebar";
import { FullscreenWrapper } from "@/components/video-editor/fullscreen-wrapper";
import { ExportDialog } from "@/components/video-editor/export-dialog";
import { KeyboardShortcutsDialog } from "@/components/video-editor/keyboard-shortcuts-dialog";
import {
  ShortcutsHelpOverlay,
  useShortcutsHelp,
  QuickShortcutsDisplay,
} from "@/components/video-editor/shortcuts-help-overlay";
import { useEnhancedShortcuts } from "@/lib/hooks/use-enhanced-shortcuts";
import type { Clip } from "@/types/video-editor";

export default function VideoEditorPage() {
  // Sidebar context
  const { isFullscreen, toggleFullscreen } = useSidebar();

  // Store hooks with selectors
  const currentProject = useProjectStore((state) => state.currentProject);
  const saveProjectToStore = useProjectStore((state) => state.saveProject);
  const isSaving = useProjectStore((state) => state.isSaving);
  const updateProject = useProjectStore((state) => state.updateProject);
  const createProject = useProjectStore((state) => state.createProject);

  // Timeline store selectors
  const tracks = useTimelineStore((state) => state.tracks);
  const duration = useTimelineStore((state) => state.duration);
  const markers = useTimelineStore((state) => state.markers);
  const playheadPosition = useTimelineStore((state) => state.playheadPosition);
  const isPlaying = useTimelineStore((state) => state.isPlaying);
  const setPlayheadPosition = useTimelineStore(
    (state) => state.setPlayheadPosition
  );
  const setIsPlaying = useTimelineStore((state) => state.setIsPlaying);
  const addTrack = useTimelineStore((state) => state.addTrack);
  const addClip = useTimelineStore((state) => state.addClip);
  const selectClip = useTimelineStore((state) => state.selectClip);
  const getTrackById = useTimelineStore((state) => state.getTrackById);
  const setDuration = useTimelineStore((state) => state.setDuration);

  // Asset store selectors
  const importAsset = useAssetStore((state) => state.importAsset);

  // Local UI state
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [showShortcutsDialog, setShowShortcutsDialog] = useState(false);

  // Keyboard shortcuts
  const { showHelp, setShowHelp } = useShortcutsHelp();

  // Performance monitoring
  const { trackVideoEdit } = useAnalyticsManager();
  const memoryManager = useMemoryManager();

  // Refs
  const playerRef = useRef<PlayerRef>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const analyticsInitialized = useRef(false);

  // Initialize project if none exists
  const hasInitialized = useRef(false);

  useEffect(() => {
    const initializeProject = async () => {
      if (!hasInitialized.current) {
        hasInitialized.current = true;
        try {
          if (!currentProject) {
            await createProject("Untitled Project", "Video editing session");
          }
        } catch (error) {
          console.error("Failed to create project:", error);
          hasInitialized.current = false; // Reset on error so we can try again
        }
      }
    };

    initializeProject();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Only run once on mount - projectStore intentionally omitted to prevent infinite loop

  // Sync playhead with player
  useEffect(() => {
    if (playerRef.current && playheadPosition !== undefined) {
      playerRef.current.seekTo(playheadPosition);
    }
  }, [playheadPosition]);

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      // Import asset using the asset store
      const asset = await importAsset(file, currentProject?.id);

      // Create a video element to get the duration
      const video = document.createElement("video");
      video.src = asset.url;
      video.onloadedmetadata = async () => {
        const duration = video.duration;

        // Create or get the main video track
        let videoTrack = tracks.find((track) => track.type === "video");
        if (!videoTrack) {
          addTrack({
            type: "video",
            name: "Video Track",
            clips: [],
            muted: false,
            locked: false,
            height: 100,
          });
          videoTrack = tracks.find((track) => track.type === "video");
        }

        if (videoTrack) {
          // Add clip to timeline
          const newClip: Omit<Clip, "id"> = {
            assetId: asset.id,
            startTime: 0,
            endTime: duration,
            trimStart: 0,
            trimEnd: duration,
            effects: [],
            properties: {
              volume: 1,
              opacity: 1,
              scale: 1,
              rotation: 0,
              filters: {
                brightness: 100,
                contrast: 100,
                saturation: 100,
              },
            },
          };

          addClip(videoTrack.id, newClip);

          // Select the new clip
          const updatedTrack = getTrackById(videoTrack.id);
          if (updatedTrack && updatedTrack.clips.length > 0) {
            const addedClip = updatedTrack.clips[updatedTrack.clips.length - 1];
            selectClip(addedClip.id);
          }
        }

        // Update timeline duration
        setDuration(Math.max(video.duration, duration));
      };
    } catch (error) {
      console.error("Failed to import asset:", error);
      alert("Failed to import video file");
    }
  };

  const triggerFileUpload = () => {
    fileInputRef.current?.click();
  };

  const saveProject = async () => {
    try {
      if (currentProject) {
        // Update project timeline with current timeline state
        updateProject({
          timeline: {
            tracks: tracks,
            duration: duration,
            markers: markers,
          },
        });

        await saveProjectToStore();
        alert("Project saved successfully!");
      }
    } catch (error) {
      console.error("Failed to save project:", error);
      alert("Failed to save project");
    }
  };

  const handleExportClick = () => {
    setShowExportDialog(true);
  };

  const handleImportClick = () => {
    fileInputRef.current?.click();
  };

  const handleNewProject = async () => {
    if (confirm("Create a new project? Unsaved changes will be lost.")) {
      await createProject("New Project", "Created from editor");
    }
  };

  // Initialize enhanced keyboard shortcuts
  useEnhancedShortcuts({
    onTogglePlayback: () => setIsPlaying(!isPlaying),
    onSeekToStart: () => setPlayheadPosition(0),
    onSeekToEnd: () => setPlayheadPosition(duration),
    onSave: saveProject,
    onExport: handleExportClick,
    onImport: handleImportClick,
    onNewProject: handleNewProject,
  });

  // Global shortcut for opening shortcuts dialog
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if ((event.ctrlKey || event.metaKey) && event.key === "k") {
        event.preventDefault();
        setShowShortcutsDialog(true);
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, []);

  // Initialize performance monitoring
  useEffect(() => {
    if (analyticsInitialized.current) return;

    analyticsInitialized.current = true;
    setupWebVitalsTracking();

    // Track page load
    trackVideoEdit("editor_loaded", {
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
    });
  }, [trackVideoEdit]);

  // Memory monitoring in separate effect
  useEffect(() => {
    const memoryInterval = setInterval(() => {
      const usage = memoryManager.getMemoryUsage();
      if (usage.percentage > 90) {
        console.warn("High memory usage detected:", usage);
        memoryManager.runGarbageCollection();
      }
    }, 30000);

    return () => clearInterval(memoryInterval);
  }, [memoryManager]);

  // These functions are now handled by the RemotionTimeline component
  // const handleClipSelect = (clipId: string) => {
  //   timelineStore.selectClip(clipId);
  // };

  // const handleClipDelete = (clipId: string) => {
  //   timelineStore.removeClip(clipId);
  // };

  // These are now handled by the RemotionPreview component
  // const videoStyles = {
  //   transform: `scale(${scale})`,
  //   filter: `brightness(${brightness}%) contrast(${contrast}%) saturate(${saturation}%)`,
  // };

  // const videoTracks = timelineStore.tracks.filter(
  //   (track) => track.type === "video"
  // );
  // const allClips = videoTracks.flatMap((track) =>
  //   track.clips.map((clip) => ({
  //     ...clip,
  //     trackId: track.id,
  //     asset: assetStore.getAssetById(clip.assetId),
  //   }))
  // );

  return (
    <ErrorBoundary>
      <FullscreenWrapper>
        <div
          className={`${
            isFullscreen ? "h-screen" : "h-full"
          } flex flex-col bg-gray-950`}
        >
          {/* Top Header Bar */}
          <div className="flex-shrink-0 bg-gray-900 border-b border-gray-700 px-6 py-4">
            <div className="flex justify-between items-center">
              <div className="flex items-center space-x-4">
                <h1 className="text-2xl font-bold text-white">Video Editor</h1>
                {currentProject && (
                  <span className="text-gray-400 text-sm">
                    {currentProject.name}
                  </span>
                )}
              </div>
              <div className="flex items-center space-x-3">
                <Button
                  onClick={triggerFileUpload}
                  size="sm"
                  className="bg-purple-600 hover:bg-purple-700"
                >
                  Upload Video
                </Button>
                <input
                  type="file"
                  accept="video/*"
                  className="hidden"
                  ref={fileInputRef}
                  onChange={handleFileUpload}
                />

                <Button
                  onClick={handleExportClick}
                  disabled={!currentProject}
                  size="sm"
                  className="bg-green-600 hover:bg-green-700"
                >
                  Export Video
                </Button>
                <Button
                  onClick={() => setShowShortcutsDialog(true)}
                  size="sm"
                  variant="outline"
                  title="Keyboard Shortcuts (Ctrl+K)"
                >
                  <Keyboard className="w-4 h-4" />
                </Button>
                <Button
                  onClick={() => setShowHelp(true)}
                  size="sm"
                  variant="outline"
                  title="Help (?)"
                >
                  <HelpCircle className="w-4 h-4" />
                </Button>
                <Button
                  onClick={toggleFullscreen}
                  size="sm"
                  variant="outline"
                  title={
                    isFullscreen
                      ? "Exit Fullscreen (F11)"
                      : "Enter Fullscreen (F11)"
                  }
                >
                  {isFullscreen ? (
                    <Minimize className="w-4 h-4" />
                  ) : (
                    <Maximize className="w-4 h-4" />
                  )}
                </Button>
                <PerformanceDashboard />
              </div>
            </div>
          </div>

          {/* Main Content Area */}
          <div className="flex-1 flex overflow-hidden relative">
            {/* Left Sidebar - Asset Manager */}
            <ResponsiveSidebar
              side="left"
              title="Assets"
              className="hidden lg:block"
            >
              <ClipManager className="h-full" />
            </ResponsiveSidebar>

            {/* Center Area - Preview and Timeline */}
            <div className="flex-1 flex flex-col overflow-hidden min-w-0">
              {/* Video Preview */}
              <div className="flex-1 p-4 lg:p-6 bg-gray-900">
                <RemotionPreview
                  className="w-full h-full mx-auto"
                  width={1920}
                  height={1080}
                  fps={30}
                />
              </div>

              {/* Timeline */}
              <div className="flex-shrink-0 bg-gray-800 border-t border-gray-700">
                <OptimizedTimeline
                  containerHeight={isFullscreen ? 400 : 320}
                  className={isFullscreen ? "h-96" : "h-80"}
                />
              </div>
            </div>

            {/* Mobile Sidebars */}
            <div className="lg:hidden">
              <ResponsiveSidebar side="left" title="Assets" defaultOpen={false}>
                <ClipManager className="h-full" />
              </ResponsiveSidebar>
            </div>
          </div>

          {/* Status Bar */}
          <div className="flex-shrink-0 bg-gray-800 border-t border-gray-700 px-6 py-2">
            <div className="flex items-center justify-between">
              <QuickShortcutsDisplay />
              <div className="text-xs text-gray-400">
                Press{" "}
                <span className="font-mono bg-gray-700 px-1 rounded">?</span>{" "}
                for help
              </div>
            </div>
          </div>

          {/* Dialogs */}
          <ExportDialog
            open={showExportDialog}
            onOpenChange={setShowExportDialog}
            projectId={currentProject?.id}
          />

          <KeyboardShortcutsDialog
            open={showShortcutsDialog}
            onOpenChange={setShowShortcutsDialog}
          />

          <ShortcutsHelpOverlay open={showHelp} onOpenChange={setShowHelp} />

          {/* Global Components */}
          <ToastContainer />
          <GlobalLoadingIndicator />
        </div>
      </FullscreenWrapper>
    </ErrorBoundary>
  );
}
