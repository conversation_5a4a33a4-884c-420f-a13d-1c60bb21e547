"use client";

import { useState, useRef, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useAssetStore } from "@/lib/stores/asset-store";
import { useProjectStore } from "@/lib/stores/project-store";
import { useTimelineStore } from "@/lib/stores/timeline-store";
import { toast } from "sonner";
import { useMediaImportWithErrorHandling } from "@/lib/hooks/use-media-import-with-error-handling";
import {
  LoadingOverlay,
  InlineLoading,
} from "@/components/ui/loading-indicators";
import { SectionErrorBoundary } from "@/components/ui/error-boundary";
import {
  Upload,
  Search,
  Filter,
  FolderPlus,
  SortAsc,
  SortDesc,
  Loader2,
  Wand2,
  FolderOpen,
} from "lucide-react";
import {
  Dropdown<PERSON>enu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { AssetLibrary } from "./asset-library";
import { FolderNavigation } from "./folder-navigation";
import { FileUploadZone } from "./file-upload-zone";
import { CreateFolderDialog } from "./create-folder-dialog";
import { AIVideoGenerator } from "./ai-video-generator";

interface ClipManagerProps {
  className?: string;
}

export function ClipManager({ className }: ClipManagerProps) {
  const sortBy = useAssetStore((state) => state.sortBy);
  const sortOrder = useAssetStore((state) => state.sortOrder);
  const filterType = useAssetStore((state) => state.filterType);
  const searchQuery = useAssetStore((state) => state.searchQuery);
  const setSearchQuery = useAssetStore((state) => state.setSearchQuery);
  const setFilterType = useAssetStore((state) => state.setFilterType);
  const setSorting = useAssetStore((state) => state.setSorting);
  const loadAssets = useAssetStore((state) => state.loadAssets);
  const assets = useAssetStore((state) => state.assets);

  const currentProject = useProjectStore((state) => state.currentProject);

  const timelineStore = useTimelineStore();

  const { importMultipleFiles, isImporting } =
    useMediaImportWithErrorHandling();

  const [showCreateFolder, setShowCreateFolder] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const isCurrentlyImporting = isImporting();

  const handleFileSelect = useCallback(
    async (files: FileList) => {
      const fileArray = Array.from(files);

      try {
        await importMultipleFiles(fileArray, {
          maxFileSize: 500 * 1024 * 1024, // 500MB
          retryOnFailure: true,
        });
      } catch (error) {
        console.error("Failed to import files:", error);
      }
    },
    [importMultipleFiles]
  );

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files) {
      handleFileSelect(files);
    }
    e.target.value = "";
  };

  const triggerFileUpload = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  const handleSortChange = useCallback(
    (newSortBy: "name" | "date" | "type" | "size") => {
      setSorting(
        newSortBy,
        sortBy === newSortBy && sortOrder === "asc" ? "desc" : "asc"
      );
    },
    [sortBy, sortOrder, setSorting]
  );

  // Add asset to timeline function
  const addAssetToTimeline = useCallback(
    (asset: { id: string; type: string; duration?: number }) => {
      // Find or create appropriate track
      let targetTrack = timelineStore.tracks.find(
        (track) =>
          track.type === asset.type ||
          (asset.type === "ai-generated" && track.type === "video") ||
          (asset.type === "image" && track.type === "video")
      );

      if (!targetTrack) {
        // Create new track if none exists
        let trackType: "video" | "audio" = "video";
        if (asset.type === "audio") {
          trackType = "audio";
        } else {
          trackType = "video";
        }

        const existingTracks = timelineStore.tracks.filter(
          (t) => t.type === trackType
        ).length;

        timelineStore.addTrack({
          type: trackType,
          name: `${
            trackType.charAt(0).toUpperCase() + trackType.slice(1)
          } Track ${existingTracks + 1}`,
          clips: [],
          muted: false,
          locked: false,
          height: trackType === "audio" ? 80 : 100,
        });

        targetTrack = timelineStore.tracks.find(
          (track) => track.type === trackType
        );
      }

      if (targetTrack) {
        const newClip = {
          assetId: asset.id,
          startTime: timelineStore.duration,
          endTime: timelineStore.duration + (asset.duration || 5),
          trimStart: 0,
          trimEnd: asset.duration || 5,
          effects: [],
          properties: {
            volume: 1,
            opacity: 1,
            position: { x: 0, y: 0 },
            scale: 1,
            rotation: 0,
            colorGrading: {
              brightness: 100,
              contrast: 100,
              saturation: 100,
            },
          },
        };

        timelineStore.addClip(targetTrack.id, newClip);
        timelineStore.setDuration(
          Math.max(timelineStore.duration, newClip.endTime)
        );
      }
    },
    [timelineStore]
  );

  const handleVideoGenerated = useCallback(
    async (assetId: string) => {
      // Reload assets to get the new video
      await loadAssets(currentProject?.id);

      // Find the generated asset and add it to timeline
      setTimeout(() => {
        const generatedAsset = assets.find((asset) => asset.id === assetId);
        if (generatedAsset) {
          // Add the generated video to timeline automatically
          addAssetToTimeline(generatedAsset);
          toast.success("Video added to timeline!");
        }
      }, 1000); // Small delay to ensure assets are loaded
    },
    [loadAssets, currentProject?.id, assets, addAssetToTimeline]
  );

  const handleFilterChange = useCallback(
    (type: "all" | "video" | "audio" | "image" | "ai-generated") => {
      setFilterType(type);
    },
    [setFilterType]
  );

  return (
    <SectionErrorBoundary sectionName="Asset Manager" className={className}>
      <LoadingOverlay
        loading={isCurrentlyImporting}
        message="Importing media files..."
      >
        <div className={`bg-gray-900 rounded-lg p-4 space-y-4 ${className}`}>
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <h3 className="text-xl font-bold text-white">Asset Manager</h3>
              <InlineLoading loading={isCurrentlyImporting} size="sm" />
            </div>
          </div>

          {/* Tabbed Interface */}
          <Tabs
            defaultValue="assets"
            className="w-full"
            key="clip-manager-tabs"
          >
            <TabsList className="grid w-full grid-cols-2 bg-gray-800">
              <TabsTrigger
                value="assets"
                className="data-[state=active]:bg-gray-700"
              >
                <FolderOpen className="w-4 h-4 mr-2" />
                Assets
              </TabsTrigger>
              <TabsTrigger
                value="ai-generator"
                className="data-[state=active]:bg-purple-600"
              >
                <Wand2 className="w-4 h-4 mr-2" />
                AI Generator
              </TabsTrigger>
            </TabsList>

            <TabsContent value="assets" className="space-y-4 mt-4">
              {/* Asset Management Controls */}
              <div className="flex items-center gap-2">
                <Button
                  onClick={triggerFileUpload}
                  disabled={isCurrentlyImporting}
                  className="bg-purple-600 hover:bg-purple-700"
                >
                  {isCurrentlyImporting ? (
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  ) : (
                    <Upload className="w-4 h-4 mr-2" />
                  )}
                  Upload
                </Button>
                <Button
                  onClick={() => setShowCreateFolder(true)}
                  variant="outline"
                  className="border-gray-600 text-gray-300 hover:bg-gray-800"
                >
                  <FolderPlus className="w-4 h-4 mr-2" />
                  New Folder
                </Button>
              </div>

              {/* Folder Navigation */}
              <FolderNavigation />

              {/* File Upload Zone */}
              <FileUploadZone onFilesSelected={handleFileSelect} />

              {/* Asset Library */}
              <AssetLibrary />
            </TabsContent>

            <TabsContent value="ai-generator" className="mt-4">
              <AIVideoGenerator
                className="h-full"
                onVideoGenerated={handleVideoGenerated}
              />
            </TabsContent>
          </Tabs>

          {/* Hidden File Input */}
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileInputChange}
            multiple
            accept="video/*,audio/*,image/*"
            className="hidden"
          />

          {/* Create Folder Dialog */}
          <CreateFolderDialog
            open={showCreateFolder}
            onOpenChange={setShowCreateFolder}
          />
        </div>
      </LoadingOverlay>
    </SectionErrorBoundary>
  );
}
